// API相关常量
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

// 本地存储键名
export const STORAGE_KEYS = {
  TOKEN: 'library_admin_token',
  USER_INFO: 'library_admin_user_info',
  THEME: 'library_admin_theme',
  LANGUAGE: 'library_admin_language',
} as const;

// 路由路径常量
export const ROUTES = {
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  BOOKS: '/books',
  BOOK_LIST: '/books/list',
  BOOK_ADD: '/books/add',
  BOOK_EDIT: '/books/edit',
  BORROW_RECORDS: '/borrow-records',
  ROBOT_STATUS: '/robot-status',
  FAULT_REPORTS: '/fault-reports',
  USER_MANAGEMENT: '/user-management',
  ROLE_MANAGEMENT: '/role-management',
  SETTINGS: '/settings',
  PROFILE: '/profile',
} as const;

// 权限代码常量
export const PERMISSIONS = {
  // 图书管理权限
  BOOK_VIEW: 'book:view',
  BOOK_ADD: 'book:add',
  BOOK_EDIT: 'book:edit',
  BOOK_DELETE: 'book:delete',
  
  // 借还书记录权限
  BORROW_VIEW: 'borrow:view',
  BORROW_MANAGE: 'borrow:manage',
  
  // 机器人管理权限
  ROBOT_VIEW: 'robot:view',
  ROBOT_CONTROL: 'robot:control',
  
  // 故障报告权限
  FAULT_VIEW: 'fault:view',
  FAULT_HANDLE: 'fault:handle',
  
  // 用户管理权限
  USER_VIEW: 'user:view',
  USER_ADD: 'user:add',
  USER_EDIT: 'user:edit',
  USER_DELETE: 'user:delete',
  
  // 角色管理权限
  ROLE_VIEW: 'role:view',
  ROLE_ADD: 'role:add',
  ROLE_EDIT: 'role:edit',
  ROLE_DELETE: 'role:delete',
  
  // 系统设置权限
  SETTINGS_VIEW: 'settings:view',
  SETTINGS_EDIT: 'settings:edit',
} as const;

// 用户角色常量
export const USER_ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  LIBRARIAN: 'librarian',
  OPERATOR: 'operator',
} as const;

// 机器人状态常量
export const ROBOT_STATUS = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  ERROR: 'error',
  MAINTENANCE: 'maintenance',
} as const;

// 机器人类型常量
export const ROBOT_TYPES = {
  SORTING: 'sorting',
  DELIVERY: 'delivery',
  MAINTENANCE: 'maintenance',
} as const;

// 故障严重程度常量
export const FAULT_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
} as const;

// 故障类型常量
export const FAULT_TYPES = {
  HARDWARE: 'hardware',
  SOFTWARE: 'software',
  NETWORK: 'network',
  OTHER: 'other',
} as const;

// 借书状态常量
export const BORROW_STATUS = {
  BORROWED: 'borrowed',
  RETURNED: 'returned',
  OVERDUE: 'overdue',
} as const;

// 分页默认配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: ['10', '20', '50', '100'],
  SHOW_SIZE_CHANGER: true,
  SHOW_QUICK_JUMPER: true,
} as const;

// 主题配置
export const THEME_CONFIG = {
  PRIMARY_COLOR: '#1890ff',
  SUCCESS_COLOR: '#52c41a',
  WARNING_COLOR: '#faad14',
  ERROR_COLOR: '#f5222d',
} as const;
