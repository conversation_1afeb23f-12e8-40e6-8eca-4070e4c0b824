import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Typography,
  Card,
  Input,
  Modal,
  Form,
  message,
  Tag,
  Popconfirm,
  Tooltip,
  Row,
  Col,
  Checkbox,
  Divider,
  Select,
  Transfer,
  Tree,
} from 'antd';
import type { TransferDirection } from 'antd/es/transfer';
import type { DataNode } from 'antd/es/tree';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  SafetyOutlined,
  ReloadOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import './index.css';

const { Title, Text } = Typography;

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  userCount: number;
  createdAt: string;
  status: 'active' | 'inactive';
}

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

const RoleManagement: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([
    {
      id: 'admin',
      name: '系统管理员',
      description: '拥有系统所有权限，可以管理用户、角色和系统设置',
      permissions: ['user:view', 'user:add', 'user:edit', 'user:delete', 'role:view', 'role:add', 'role:edit', 'role:delete', 'book:view', 'book:add', 'book:edit', 'book:delete', 'borrow:view', 'borrow:manage', 'robot:view', 'robot:manage', 'fault:view', 'fault:handle', 'system:settings'],
      userCount: 1,
      createdAt: '2024-01-01',
      status: 'active',
    },
    {
      id: 'librarian',
      name: '图书管理员',
      description: '负责图书管理和借还记录管理',
      permissions: ['book:view', 'book:add', 'book:edit', 'book:delete', 'borrow:view', 'borrow:manage', 'robot:view', 'fault:view'],
      userCount: 3,
      createdAt: '2024-01-02',
      status: 'active',
    },
    {
      id: 'reader',
      name: '读者',
      description: '普通读者，只能查看和借阅图书',
      permissions: ['book:view', 'borrow:view'],
      userCount: 15,
      createdAt: '2024-01-03',
      status: 'active',
    },
  ]);

  const [permissions] = useState<Permission[]>([
    // 用户管理权限
    { id: 'user:view', name: '查看用户', description: '查看用户列表和详情', category: 'user' },
    { id: 'user:add', name: '新增用户', description: '创建新用户账户', category: 'user' },
    { id: 'user:edit', name: '编辑用户', description: '修改用户信息', category: 'user' },
    { id: 'user:delete', name: '删除用户', description: '删除用户账户', category: 'user' },

    // 角色管理权限
    { id: 'role:view', name: '查看角色', description: '查看角色列表和权限', category: 'role' },
    { id: 'role:add', name: '新增角色', description: '创建新角色', category: 'role' },
    { id: 'role:edit', name: '编辑角色', description: '修改角色权限', category: 'role' },
    { id: 'role:delete', name: '删除角色', description: '删除角色', category: 'role' },

    // 图书管理权限
    { id: 'book:view', name: '查看图书', description: '查看图书列表和详情', category: 'book' },
    { id: 'book:add', name: '新增图书', description: '添加新图书', category: 'book' },
    { id: 'book:edit', name: '编辑图书', description: '修改图书信息', category: 'book' },
    { id: 'book:delete', name: '删除图书', description: '删除图书记录', category: 'book' },

    // 借还管理权限
    { id: 'borrow:view', name: '查看借还记录', description: '查看借还记录', category: 'borrow' },
    { id: 'borrow:manage', name: '管理借还', description: '处理借还操作', category: 'borrow' },

    // 机器人管理权限
    { id: 'robot:view', name: '查看机器人状态', description: '查看机器人运行状态', category: 'robot' },
    { id: 'robot:manage', name: '管理机器人', description: '控制机器人操作', category: 'robot' },

    // 故障管理权限
    { id: 'fault:view', name: '查看故障报告', description: '查看故障报告', category: 'fault' },
    { id: 'fault:handle', name: '处理故障', description: '处理和解决故障', category: 'fault' },

    // 系统管理权限
    { id: 'system:settings', name: '系统设置', description: '修改系统配置', category: 'system' },
  ]);

  // 权限树形数据
  const permissionTreeData: DataNode[] = [
    {
      title: '用户管理',
      key: 'user',
      children: [
        { title: '查看用户', key: 'user:view' },
        { title: '新增用户', key: 'user:add' },
        { title: '编辑用户', key: 'user:edit' },
        { title: '删除用户', key: 'user:delete' },
      ],
    },
    {
      title: '角色管理',
      key: 'role',
      children: [
        { title: '查看角色', key: 'role:view' },
        { title: '新增角色', key: 'role:add' },
        { title: '编辑角色', key: 'role:edit' },
        { title: '删除角色', key: 'role:delete' },
      ],
    },
    {
      title: '图书管理',
      key: 'book',
      children: [
        { title: '查看图书', key: 'book:view' },
        { title: '新增图书', key: 'book:add' },
        { title: '编辑图书', key: 'book:edit' },
        { title: '删除图书', key: 'book:delete' },
      ],
    },
    {
      title: '借还管理',
      key: 'borrow',
      children: [
        { title: '查看借还记录', key: 'borrow:view' },
        { title: '管理借还', key: 'borrow:manage' },
      ],
    },
    {
      title: '机器人管理',
      key: 'robot',
      children: [
        { title: '查看机器人状态', key: 'robot:view' },
        { title: '管理机器人', key: 'robot:manage' },
      ],
    },
    {
      title: '故障管理',
      key: 'fault',
      children: [
        { title: '查看故障报告', key: 'fault:view' },
        { title: '处理故障', key: 'fault:handle' },
      ],
    },
    {
      title: '系统管理',
      key: 'system',
      children: [
        { title: '系统设置', key: 'system:settings' },
      ],
    },
  ];

  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [searchText, setSearchText] = useState('');
  const [form] = Form.useForm();
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [permissionMode, setPermissionMode] = useState<'tree' | 'transfer'>('tree');

  const getCategoryName = (category: string) => {
    const categoryMap: Record<string, string> = {
      user: '用户管理',
      role: '角色管理',
      book: '图书管理',
      borrow: '借还管理',
      robot: '机器人管理',
      fault: '故障管理',
      system: '系统管理',
    };
    return categoryMap[category] || category;
  };

  // 处理角色操作
  const handleAddRole = () => {
    setEditingRole(null);
    setSelectedPermissions([]);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditRole = (role: Role) => {
    setEditingRole(role);
    setSelectedPermissions(role.permissions);
    form.setFieldsValue({
      ...role,
      permissions: role.permissions,
    });
    setModalVisible(true);
  };

  const handleDeleteRole = async (roleId: string) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setRoles(prev => prev.filter(r => r.id !== roleId));
      message.success('角色删除成功');
    } catch (error) {
      message.error('删除失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveRole = async (values: any) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (editingRole) {
        // 编辑角色
        setRoles(prev => prev.map(r =>
          r.id === editingRole.id
            ? { ...r, ...values }
            : r
        ));
        message.success('角色信息更新成功');
      } else {
        // 新增角色
        const newRole: Role = {
          id: Date.now().toString(),
          ...values,
          userCount: 0,
          status: 'active',
          createdAt: new Date().toISOString().split('T')[0],
        };
        setRoles(prev => [...prev, newRole]);
        message.success('角色创建成功');
      }

      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  // 过滤角色数据
  const filteredRoles = roles.filter(role =>
    !searchText ||
    role.name.toLowerCase().includes(searchText.toLowerCase()) ||
    role.description.toLowerCase().includes(searchText.toLowerCase())
  );

  // 表格列配置
  const columns: ColumnsType<Role> = [
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (name, record) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>{name}</div>
          <Tag color={record.status === 'active' ? 'green' : 'red'}>
            {record.status === 'active' ? '启用' : '禁用'}
          </Tag>
        </div>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '权限数量',
      dataIndex: 'permissions',
      key: 'permissionCount',
      width: 100,
      render: (permissions) => (
        <Tag color="blue">{permissions.length} 项</Tag>
      ),
    },
    {
      title: '用户数量',
      dataIndex: 'userCount',
      key: 'userCount',
      width: 100,
      render: (count) => (
        <span style={{ color: count > 0 ? '#1890ff' : '#999' }}>
          {count} 人
        </span>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEditRole(record)}
              size="small"
            />
          </Tooltip>

          <Tooltip title="权限详情">
            <Button
              type="link"
              icon={<SettingOutlined />}
              onClick={() => {
                Modal.info({
                  title: `${record.name} - 权限详情`,
                  width: 600,
                  content: (
                    <div style={{ marginTop: 16 }}>
                      {Object.entries(
                        record.permissions.reduce((acc, permId) => {
                          const perm = permissions.find(p => p.id === permId);
                          if (perm) {
                            if (!acc[perm.category]) acc[perm.category] = [];
                            acc[perm.category].push(perm);
                          }
                          return acc;
                        }, {} as Record<string, Permission[]>)
                      ).map(([category, perms]) => (
                        <div key={category} style={{ marginBottom: 16 }}>
                          <div style={{ fontWeight: 500, marginBottom: 8 }}>
                            {getCategoryName(category)}
                          </div>
                          <div>
                            {perms.map(perm => (
                              <Tag key={perm.id} style={{ marginBottom: 4 }}>
                                {perm.name}
                              </Tag>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  ),
                });
              }}
              size="small"
            />
          </Tooltip>

          {record.userCount === 0 && (
            <Popconfirm
              title="确定要删除这个角色吗？"
              onConfirm={() => handleDeleteRole(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="link"
                  icon={<DeleteOutlined />}
                  size="small"
                  style={{ color: '#ff4d4f' }}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="role-management">
      <div className="page-header">
        <Title level={2}>
          <SafetyOutlined /> 角色管理
        </Title>
        <Text type="secondary">管理系统角色和权限配置</Text>
      </div>

      <Card>
        {/* 搜索和操作区域 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={8}>
            <Input
              placeholder="搜索角色名称或描述"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col xs={24} sm={12} md={16}>
            <Space style={{ float: 'right' }}>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => setSearchText('')}
              >
                重置
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddRole}
              >
                新增角色
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 角色表格 */}
        <Table
          columns={columns}
          dataSource={filteredRoles}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredRoles.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 角色编辑模态框 */}
      <Modal
        title={editingRole ? '编辑角色' : '新增角色'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveRole}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="角色名称"
                name="name"
                rules={[
                  { required: true, message: '请输入角色名称' },
                  { min: 2, message: '角色名称至少2个字符' }
                ]}
              >
                <Input placeholder="请输入角色名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="状态"
                name="status"
                rules={[{ required: true, message: '请选择状态' }]}
                initialValue="active"
              >
                <Select>
                  <Select.Option value="active">启用</Select.Option>
                  <Select.Option value="inactive">禁用</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="角色描述"
            name="description"
            rules={[{ required: true, message: '请输入角色描述' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入角色描述"
              maxLength={200}
              showCount
            />
          </Form.Item>

          <Form.Item
            label={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                权限配置
                <Select
                  size="small"
                  value={permissionMode}
                  onChange={setPermissionMode}
                  style={{ width: 100 }}
                  options={[
                    { label: '树形', value: 'tree' },
                    { label: '穿梭框', value: 'transfer' },
                  ]}
                />
              </div>
            }
            name="permissions"
            rules={[{ required: true, message: '请选择权限' }]}
          >
            {permissionMode === 'tree' ? (
              <div style={{
                border: '1px solid #d9d9d9',
                borderRadius: 6,
                padding: 12,
                maxHeight: 400,
                overflow: 'auto'
              }}>
                <Tree
                  checkable
                  checkedKeys={selectedPermissions}
                  onCheck={(checkedKeys) => {
                    const keys = Array.isArray(checkedKeys) ? checkedKeys : checkedKeys.checked;
                    const leafKeys = keys.filter(key =>
                      !permissionTreeData.some(node => node.key === key)
                    );
                    setSelectedPermissions(leafKeys as string[]);
                    form.setFieldValue('permissions', leafKeys);
                  }}
                  treeData={permissionTreeData}
                  defaultExpandAll
                />
              </div>
            ) : (
              <Transfer
                dataSource={permissions.map(perm => ({
                  key: perm.id,
                  title: perm.name,
                  description: `${getCategoryName(perm.category)} - ${perm.description}`,
                }))}
                targetKeys={selectedPermissions}
                onChange={(targetKeys) => {
                  setSelectedPermissions(targetKeys);
                  form.setFieldValue('permissions', targetKeys);
                }}
                render={item => `${item.title} - ${item.description}`}
                titles={['可选权限', '已选权限']}
                showSearch
                filterOption={(inputValue, option) =>
                  option.title.indexOf(inputValue) > -1 ||
                  option.description.indexOf(inputValue) > -1
                }
                style={{ width: '100%' }}
                listStyle={{
                  width: 300,
                  height: 300,
                }}
              />
            )}
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingRole ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RoleManagement;
