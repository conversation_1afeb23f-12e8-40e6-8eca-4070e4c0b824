.settings-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.settings-header {
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-header h2 {
  margin-bottom: 8px;
  color: #1890ff;
}

.settings-form {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.settings-card {
  margin-bottom: 0;
  border: none;
  box-shadow: none;
}

.settings-card .ant-card-head {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.settings-card .ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

.settings-card .ant-card-body {
  padding: 24px;
}

.settings-actions {
  padding: 24px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

.ant-tabs-content-holder {
  padding: 0;
}

.ant-tabs-tabpane {
  padding: 0;
}

/* 表单项样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-form-item-extra {
  color: #8c8c8c;
  font-size: 12px;
}

/* Switch 组件样式 */
.ant-switch-checked {
  background-color: #52c41a;
}

/* 输入框聚焦效果 */
.ant-input:focus,
.ant-input-focused,
.ant-input-number:focus,
.ant-input-number-focused,
.ant-select-focused .ant-select-selector,
.ant-picker:focus,
.ant-picker-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 卡片标题图标 */
.settings-card .ant-card-head-title .anticon {
  margin-right: 8px;
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-container {
    padding: 16px;
  }
  
  .settings-header {
    padding: 16px;
  }
  
  .settings-card .ant-card-body {
    padding: 16px;
  }
  
  .settings-actions {
    padding: 16px;
    text-align: center;
  }
}

/* 标签页样式优化 */
.ant-tabs-tab {
  padding: 12px 16px;
}

.ant-tabs-tab .anticon {
  margin-right: 6px;
}

.ant-tabs-ink-bar {
  background: #1890ff;
}

.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #1890ff;
  font-weight: 600;
}

/* 按钮样式 */
.settings-actions .ant-btn {
  min-width: 80px;
  height: 36px;
}

.settings-actions .ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.settings-actions .ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}
