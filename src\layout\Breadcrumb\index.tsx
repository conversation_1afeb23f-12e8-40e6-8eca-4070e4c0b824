import React from 'react';
import { Breadcrumb as AntBreadcrumb } from 'antd';
import { useLocation, Link } from 'react-router';
import { HomeOutlined } from '@ant-design/icons';
import { ROUTES } from '../../constants';
import './index.css';

// 路径映射配置
const breadcrumbNameMap: Record<string, string> = {
  [ROUTES.DASHBOARD]: '仪表盘',
  [ROUTES.BOOK_LIST]: '图书列表',
  [ROUTES.BOOK_ADD]: '添加图书',
  [ROUTES.BOOK_EDIT]: '编辑图书',
  [ROUTES.BORROW_RECORDS]: '借还记录',
  [ROUTES.ROBOT_STATUS]: '机器人状态',
  [ROUTES.FAULT_REPORTS]: '故障报告',
  [ROUTES.USER_MANAGEMENT]: '用户管理',
  [ROUTES.ROLE_MANAGEMENT]: '角色管理',
  [ROUTES.SETTINGS]: '系统设置',
  '/books': '图书管理',
  '/system': '系统管理',
};

const Breadcrumb: React.FC = () => {
  const location = useLocation();
  const pathSnippets = location.pathname.split('/').filter((i) => i);

  // 生成面包屑项目
  const breadcrumbItems = [
    {
      title: (
        <Link to={ROUTES.DASHBOARD}>
          <HomeOutlined style={{ marginRight: '4px' }} />
          首页
        </Link>
      ),
    },
  ];

  // 构建面包屑路径
  let currentPath = '';
  pathSnippets.forEach((snippet, index) => {
    currentPath += `/${snippet}`;
    const isLast = index === pathSnippets.length - 1;
    const breadcrumbName = breadcrumbNameMap[currentPath];

    if (breadcrumbName) {
      breadcrumbItems.push({
        title: isLast ? (
          breadcrumbName
        ) : (
          <Link to={currentPath}>{breadcrumbName}</Link>
        ),
      });
    }
  });

  // 如果只有首页，不显示面包屑
  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <div className="breadcrumb-container">
      <AntBreadcrumb items={breadcrumbItems} />
    </div>
  );
};

export default Breadcrumb;
