import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  Button,
  Card,
  Typography,
  Row,
  Col,
  message,
  Space,
} from 'antd';
import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router';
import dayjs from 'dayjs';
import type { Book } from '../../../types/index';
import { addBook, getBookCategories } from '../../../services/bookService';
import { ROUTES } from '../../../constants';
import PermissionGuard from '../../../components/PermissionGuard';
import { PERMISSIONS } from '../../../constants';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

type BookFormData = Omit<Book, 'id' | 'createdAt' | 'updatedAt'>;

const BookAdd: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean>(false);
  const [categories, setCategories] = useState<string[]>([]);

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await getBookCategories();
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('获取分类失败:', error);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const bookData: BookFormData = {
        ...values,
        publishDate: values.publishDate.format('YYYY-MM-DD'),
      };

      const response = await addBook(bookData);
      if (response.success) {
        message.success('图书添加成功！');
        navigate(ROUTES.BOOK_LIST, {
          state: {
            success: '图书添加成功！',
            newBookId: response.data.id
          }
        });
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('添加图书失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理返回 - 使用 React Router v7 的新导航方式
  const handleBack = () => {
    navigate(ROUTES.BOOK_LIST, {
      state: { from: window.location.pathname }
    });
  };

  return (
    <PermissionGuard permission={PERMISSIONS.BOOK_ADD}>
      <div>
        <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            style={{ marginRight: 16 }}
          >
            返回
          </Button>
          <Title level={2} style={{ margin: 0 }}>添加图书</Title>
        </div>

        <Card>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              totalCopies: 1,
              availableCopies: 1,
            }}
          >
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="书名"
                  name="title"
                  rules={[
                    { required: true, message: '请输入书名' },
                    { max: 200, message: '书名不能超过200个字符' },
                  ]}
                >
                  <Input placeholder="请输入书名" />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label="作者"
                  name="author"
                  rules={[
                    { required: true, message: '请输入作者' },
                    { max: 100, message: '作者不能超过100个字符' },
                  ]}
                >
                  <Input placeholder="请输入作者" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="ISBN"
                  name="isbn"
                  rules={[
                    { required: true, message: '请输入ISBN' },
                    { pattern: /^[0-9-]+$/, message: 'ISBN格式不正确' },
                  ]}
                >
                  <Input placeholder="请输入ISBN" />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label="分类"
                  name="category"
                  rules={[{ required: true, message: '请选择分类' }]}
                >
                  <Select
                    placeholder="请选择分类"
                    showSearch
                    allowClear
                    optionFilterProp="children"
                  >
                    {categories.map(category => (
                      <Option key={category} value={category}>
                        {category}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="出版社"
                  name="publisher"
                  rules={[
                    { required: true, message: '请输入出版社' },
                    { max: 100, message: '出版社不能超过100个字符' },
                  ]}
                >
                  <Input placeholder="请输入出版社" />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label="出版日期"
                  name="publishDate"
                  rules={[{ required: true, message: '请选择出版日期' }]}
                >
                  <DatePicker
                    style={{ width: '100%' }}
                    placeholder="请选择出版日期"
                    disabledDate={(current) => current && current > dayjs().endOf('day')}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} md={8}>
                <Form.Item
                  label="总册数"
                  name="totalCopies"
                  rules={[
                    { required: true, message: '请输入总册数' },
                    { type: 'number', min: 1, message: '总册数不能小于1' },
                  ]}
                >
                  <InputNumber
                    min={1}
                    max={9999}
                    style={{ width: '100%' }}
                    placeholder="请输入总册数"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item
                  label="可借册数"
                  name="availableCopies"
                  rules={[
                    { required: true, message: '请输入可借册数' },
                    { type: 'number', min: 0, message: '可借册数不能小于0' },
                  ]}
                >
                  <InputNumber
                    min={0}
                    max={9999}
                    style={{ width: '100%' }}
                    placeholder="请输入可借册数"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item
                  label="存放位置"
                  name="location"
                  rules={[
                    { required: true, message: '请输入存放位置' },
                    { max: 50, message: '存放位置不能超过50个字符' },
                  ]}
                >
                  <Input placeholder="如：A区-1层-001" />
                </Form.Item>
              </Col>
            </Row>

            <Row>
              <Col span={24}>
                <Form.Item
                  label="图书描述"
                  name="description"
                  rules={[{ max: 500, message: '描述不能超过500个字符' }]}
                >
                  <TextArea
                    rows={4}
                    placeholder="请输入图书描述（可选）"
                    showCount
                    maxLength={500}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<SaveOutlined />}
                >
                  保存
                </Button>
                <Button onClick={handleBack}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </PermissionGuard>
  );
};

export default BookAdd;
