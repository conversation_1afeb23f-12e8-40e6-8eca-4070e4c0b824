import type { FaultReport, ApiResponse, PaginatedResponse, PaginationParams } from '../types/index';
import { FAULT_TYPES, FAULT_SEVERITY } from '../constants';
import dayjs from 'dayjs';

// 模拟故障报告数据
const mockFaultReports: FaultReport[] = [
  {
    id: 'fault-001',
    robotId: 'robot-004',
    robotName: '配送机器人-02',
    faultType: FAULT_TYPES.HARDWARE,
    severity: FAULT_SEVERITY.HIGH,
    description: '机器人左侧轮子卡住，无法正常移动',
    reportTime: dayjs().subtract(2, 'hour').toISOString(),
    status: 'processing',
    assignedTo: '维修工程师-张三',
  },
  {
    id: 'fault-002',
    robotId: 'robot-006',
    robotName: '分拣机器人-03',
    faultType: FAULT_TYPES.SOFTWARE,
    severity: FAULT_SEVERITY.MEDIUM,
    description: '系统响应缓慢，处理任务效率下降',
    reportTime: dayjs().subtract(5, 'hour').toISOString(),
    status: 'resolved',
    assignedTo: '软件工程师-李四',
    resolvedTime: dayjs().subtract(1, 'hour').toISOString(),
    solution: '重启系统服务，更新软件版本',
  },
  {
    id: 'fault-003',
    robotId: 'robot-001',
    robotName: '分拣机器人-01',
    faultType: FAULT_TYPES.NETWORK,
    severity: FAULT_SEVERITY.LOW,
    description: '网络连接不稳定，偶尔断线',
    reportTime: dayjs().subtract(1, 'day').toISOString(),
    status: 'closed',
    assignedTo: '网络工程师-王五',
    resolvedTime: dayjs().subtract(18, 'hour').toISOString(),
    solution: '更换网络模块，优化信号接收',
  },
  {
    id: 'fault-004',
    robotId: 'robot-003',
    robotName: '配送机器人-01',
    faultType: FAULT_TYPES.HARDWARE,
    severity: FAULT_SEVERITY.CRITICAL,
    description: '电池模块过热，存在安全隐患',
    reportTime: dayjs().subtract(30, 'minute').toISOString(),
    status: 'pending',
  },
  {
    id: 'fault-005',
    robotId: 'robot-002',
    robotName: '分拣机器人-02',
    faultType: FAULT_TYPES.OTHER,
    severity: FAULT_SEVERITY.LOW,
    description: '机器人外壳有轻微划痕，不影响功能',
    reportTime: dayjs().subtract(3, 'day').toISOString(),
    status: 'closed',
    assignedTo: '维修工程师-赵六',
    resolvedTime: dayjs().subtract(2, 'day').toISOString(),
    solution: '外壳抛光处理',
  },
];

// 获取故障报告列表
export const getFaultReports = async (
  params: PaginationParams & {
    robotName?: string;
    faultType?: string;
    severity?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
  }
): Promise<ApiResponse<PaginatedResponse<FaultReport>>> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 800));

  let filteredReports = [...mockFaultReports];

  // 搜索过滤
  if (params.robotName) {
    filteredReports = filteredReports.filter(report =>
      report.robotName.toLowerCase().includes(params.robotName!.toLowerCase())
    );
  }
  if (params.faultType) {
    filteredReports = filteredReports.filter(report =>
      report.faultType === params.faultType
    );
  }
  if (params.severity) {
    filteredReports = filteredReports.filter(report =>
      report.severity === params.severity
    );
  }
  if (params.status) {
    filteredReports = filteredReports.filter(report =>
      report.status === params.status
    );
  }
  if (params.startDate) {
    filteredReports = filteredReports.filter(report =>
      dayjs(report.reportTime).isAfter(dayjs(params.startDate).subtract(1, 'day'))
    );
  }
  if (params.endDate) {
    filteredReports = filteredReports.filter(report =>
      dayjs(report.reportTime).isBefore(dayjs(params.endDate).add(1, 'day'))
    );
  }

  // 按报告时间倒序排列
  filteredReports.sort((a, b) => 
    dayjs(b.reportTime).valueOf() - dayjs(a.reportTime).valueOf()
  );

  // 分页
  const total = filteredReports.length;
  const start = (params.page - 1) * params.pageSize;
  const end = start + params.pageSize;
  const list = filteredReports.slice(start, end);

  return {
    code: 200,
    message: '获取成功',
    data: {
      list,
      pagination: {
        current: params.page,
        pageSize: params.pageSize,
        total,
      },
    },
    success: true,
  };
};

// 获取故障统计数据
export const getFaultStatistics = async (): Promise<ApiResponse<{
  total: number;
  pending: number;
  processing: number;
  resolved: number;
  closed: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
}>> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  const total = mockFaultReports.length;
  const pending = mockFaultReports.filter(r => r.status === 'pending').length;
  const processing = mockFaultReports.filter(r => r.status === 'processing').length;
  const resolved = mockFaultReports.filter(r => r.status === 'resolved').length;
  const closed = mockFaultReports.filter(r => r.status === 'closed').length;
  
  const critical = mockFaultReports.filter(r => r.severity === FAULT_SEVERITY.CRITICAL).length;
  const high = mockFaultReports.filter(r => r.severity === FAULT_SEVERITY.HIGH).length;
  const medium = mockFaultReports.filter(r => r.severity === FAULT_SEVERITY.MEDIUM).length;
  const low = mockFaultReports.filter(r => r.severity === FAULT_SEVERITY.LOW).length;

  return {
    code: 200,
    message: '获取成功',
    data: {
      total,
      pending,
      processing,
      resolved,
      closed,
      critical,
      high,
      medium,
      low,
    },
    success: true,
  };
};

// 分配故障报告
export const assignFaultReport = async (
  id: string,
  assignedTo: string
): Promise<ApiResponse<FaultReport>> => {
  await new Promise(resolve => setTimeout(resolve, 800));

  const reportIndex = mockFaultReports.findIndex(r => r.id === id);
  if (reportIndex === -1) {
    return {
      code: 404,
      message: '故障报告不存在',
      data: null,
      success: false,
    };
  }

  const updatedReport = {
    ...mockFaultReports[reportIndex],
    assignedTo,
    status: 'processing' as const,
  };

  mockFaultReports[reportIndex] = updatedReport;

  return {
    code: 200,
    message: '分配成功',
    data: updatedReport,
    success: true,
  };
};

// 解决故障报告
export const resolveFaultReport = async (
  id: string,
  solution: string
): Promise<ApiResponse<FaultReport>> => {
  await new Promise(resolve => setTimeout(resolve, 1000));

  const reportIndex = mockFaultReports.findIndex(r => r.id === id);
  if (reportIndex === -1) {
    return {
      code: 404,
      message: '故障报告不存在',
      data: null,
      success: false,
    };
  }

  const updatedReport = {
    ...mockFaultReports[reportIndex],
    status: 'resolved' as const,
    solution,
    resolvedTime: new Date().toISOString(),
  };

  mockFaultReports[reportIndex] = updatedReport;

  return {
    code: 200,
    message: '故障已解决',
    data: updatedReport,
    success: true,
  };
};

// 关闭故障报告
export const closeFaultReport = async (id: string): Promise<ApiResponse<FaultReport>> => {
  await new Promise(resolve => setTimeout(resolve, 600));

  const reportIndex = mockFaultReports.findIndex(r => r.id === id);
  if (reportIndex === -1) {
    return {
      code: 404,
      message: '故障报告不存在',
      data: null,
      success: false,
    };
  }

  const report = mockFaultReports[reportIndex];
  if (report.status !== 'resolved') {
    return {
      code: 400,
      message: '只有已解决的故障才能关闭',
      data: null,
      success: false,
    };
  }

  const updatedReport = {
    ...report,
    status: 'closed' as const,
  };

  mockFaultReports[reportIndex] = updatedReport;

  return {
    code: 200,
    message: '故障报告已关闭',
    data: updatedReport,
    success: true,
  };
};

// 获取待处理故障数量（用于通知）
export const getPendingFaultCount = async (): Promise<ApiResponse<number>> => {
  await new Promise(resolve => setTimeout(resolve, 300));

  const pendingCount = mockFaultReports.filter(r => 
    r.status === 'pending' || r.status === 'processing'
  ).length;

  return {
    code: 200,
    message: '获取成功',
    data: pendingCount,
    success: true,
  };
};
