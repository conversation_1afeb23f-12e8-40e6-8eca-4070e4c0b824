.user-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin-bottom: 8px;
  color: #1890ff;
}

/* 表格样式优化 */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 用户信息单元格样式 */
.ant-table-tbody .ant-avatar {
  border: 2px solid #f0f0f0;
}

/* 标签样式 */
.ant-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 操作按钮样式 */
.ant-btn-link {
  padding: 4px 8px;
  height: auto;
}

.ant-btn-link:hover {
  background: rgba(24, 144, 255, 0.1);
  border-radius: 4px;
}

/* 搜索区域样式 */
.ant-input-affix-wrapper {
  border-radius: 6px;
}

.ant-select-selector {
  border-radius: 6px;
}

/* 模态框样式 */
.ant-modal-header {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-title {
  font-weight: 600;
  color: #262626;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input-password,
.ant-select-selector {
  border-radius: 6px;
}

.ant-input:focus,
.ant-input-focused,
.ant-input-password:focus,
.ant-input-password-focused,
.ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

.ant-pagination-total-text {
  color: #666;
}

/* 卡片样式 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
}

.ant-card-body {
  padding: 24px;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-management {
    padding: 16px;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .ant-card-body {
    padding: 16px;
  }
  
  /* 移动端表格滚动 */
  .ant-table-wrapper {
    overflow-x: auto;
  }
  
  /* 移动端搜索区域 */
  .ant-row {
    margin-bottom: 8px;
  }
  
  .ant-col {
    margin-bottom: 8px;
  }
}

@media (max-width: 576px) {
  .user-management {
    padding: 12px;
  }
  
  .page-header {
    padding: 12px;
  }
  
  .page-header h2 {
    font-size: 18px;
  }
  
  .ant-card-body {
    padding: 12px;
  }
  
  /* 移动端模态框 */
  .ant-modal {
    margin: 0;
    max-width: 100vw;
  }
  
  .ant-modal-content {
    border-radius: 0;
  }
}

/* 状态标签颜色优化 */
.ant-tag-green {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.ant-tag-orange {
  background: #fff7e6;
  border-color: #ffd591;
  color: #fa8c16;
}

.ant-tag-red {
  background: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

.ant-tag-blue {
  background: #f0f5ff;
  border-color: #adc6ff;
  color: #1890ff;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  background: rgba(0, 0, 0, 0.85);
  border-radius: 4px;
}

/* 确认弹框样式 */
.ant-popconfirm-inner {
  border-radius: 6px;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

.ant-empty-description {
  color: #999;
}

/* 加载状态样式 */
.ant-spin-container {
  position: relative;
}

.ant-spin-blur {
  opacity: 0.5;
  pointer-events: none;
}

/* 表格行选择样式 */
.ant-table-row-selected > td {
  background: #e6f7ff;
}

/* 搜索高亮样式 */
.search-highlight {
  background: #ffe58f;
  padding: 0 2px;
  border-radius: 2px;
}
