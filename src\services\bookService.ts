import type { Book, ApiResponse, PaginatedResponse, PaginationParams } from '../types/index';

// 模拟图书数据
const mockBooks: Book[] = [
  {
    id: '1',
    title: 'JavaScript高级程序设计',
    author: '<PERSON>',
    isbn: '9787115275790',
    category: '计算机科学',
    publisher: '人民邮电出版社',
    publishDate: '2012-03-01',
    totalCopies: 10,
    availableCopies: 7,
    location: 'A区-1层-001',
    description: '深入理解JavaScript语言核心概念和高级特性',
    coverUrl: '',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    title: 'React实战',
    author: '<PERSON>',
    isbn: '9787115456472',
    category: '计算机科学',
    publisher: '人民邮电出版社',
    publishDate: '2017-09-01',
    totalCopies: 8,
    availableCopies: 5,
    location: 'A区-1层-002',
    description: '全面掌握React开发技术',
    coverUrl: '',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  },
  {
    id: '3',
    title: 'TypeScript入门与实战',
    author: '梁灏',
    isbn: '9787121367625',
    category: '计算机科学',
    publisher: '电子工业出版社',
    publishDate: '2019-06-01',
    totalCopies: 12,
    availableCopies: 9,
    location: 'A区-1层-003',
    description: 'TypeScript从入门到精通',
    coverUrl: '',
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-03T00:00:00Z',
  },
  {
    id: '4',
    title: 'Vue.js实战',
    author: '梁灏',
    isbn: '9787121310928',
    category: '计算机科学',
    publisher: '电子工业出版社',
    publishDate: '2017-04-01',
    totalCopies: 15,
    availableCopies: 12,
    location: 'A区-1层-004',
    description: '深入浅出Vue.js框架开发',
    coverUrl: '',
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-04T00:00:00Z',
  },
  {
    id: '5',
    title: 'Node.js开发指南',
    author: '郭家宝',
    isbn: '9787115279507',
    category: '计算机科学',
    publisher: '人民邮电出版社',
    publishDate: '2012-07-01',
    totalCopies: 6,
    availableCopies: 4,
    location: 'A区-1层-005',
    description: 'Node.js服务端开发完全指南',
    coverUrl: '',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-05T00:00:00Z',
  },
];

// 获取图书列表
export const getBookList = async (
  params: PaginationParams & {
    title?: string;
    author?: string;
    category?: string;
  }
): Promise<ApiResponse<PaginatedResponse<Book>>> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 800));

  let filteredBooks = [...mockBooks];

  // 搜索过滤
  if (params.title) {
    filteredBooks = filteredBooks.filter(book =>
      book.title.toLowerCase().includes(params.title!.toLowerCase())
    );
  }
  if (params.author) {
    filteredBooks = filteredBooks.filter(book =>
      book.author.toLowerCase().includes(params.author!.toLowerCase())
    );
  }
  if (params.category) {
    filteredBooks = filteredBooks.filter(book =>
      book.category === params.category
    );
  }

  // 分页
  const total = filteredBooks.length;
  const start = (params.page - 1) * params.pageSize;
  const end = start + params.pageSize;
  const list = filteredBooks.slice(start, end);

  return {
    code: 200,
    message: '获取成功',
    data: {
      list,
      pagination: {
        current: params.page,
        pageSize: params.pageSize,
        total,
      },
    },
    success: true,
  };
};

// 获取图书详情
export const getBookDetail = async (id: string): Promise<ApiResponse<Book>> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  const book = mockBooks.find(b => b.id === id);
  if (!book) {
    return {
      code: 404,
      message: '图书不存在',
      data: null,
      success: false,
    };
  }

  return {
    code: 200,
    message: '获取成功',
    data: book,
    success: true,
  };
};

// 添加图书
export const addBook = async (bookData: Omit<Book, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Book>> => {
  await new Promise(resolve => setTimeout(resolve, 1000));

  const newBook: Book = {
    ...bookData,
    id: Date.now().toString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  mockBooks.push(newBook);

  return {
    code: 200,
    message: '添加成功',
    data: newBook,
    success: true,
  };
};

// 更新图书
export const updateBook = async (id: string, bookData: Partial<Book>): Promise<ApiResponse<Book>> => {
  await new Promise(resolve => setTimeout(resolve, 1000));

  const bookIndex = mockBooks.findIndex(b => b.id === id);
  if (bookIndex === -1) {
    return {
      code: 404,
      message: '图书不存在',
      data: null,
      success: false,
    };
  }

  const updatedBook = {
    ...mockBooks[bookIndex],
    ...bookData,
    updatedAt: new Date().toISOString(),
  };

  mockBooks[bookIndex] = updatedBook;

  return {
    code: 200,
    message: '更新成功',
    data: updatedBook,
    success: true,
  };
};

// 删除图书
export const deleteBook = async (id: string): Promise<ApiResponse<null>> => {
  await new Promise(resolve => setTimeout(resolve, 800));

  const bookIndex = mockBooks.findIndex(b => b.id === id);
  if (bookIndex === -1) {
    return {
      code: 404,
      message: '图书不存在',
      data: null,
      success: false,
    };
  }

  mockBooks.splice(bookIndex, 1);

  return {
    code: 200,
    message: '删除成功',
    data: null,
    success: true,
  };
};

// 获取图书分类列表
export const getBookCategories = async (): Promise<ApiResponse<string[]>> => {
  await new Promise(resolve => setTimeout(resolve, 300));

  const categories = Array.from(new Set(mockBooks.map(book => book.category)));

  return {
    code: 200,
    message: '获取成功',
    data: categories,
    success: true,
  };
};
