import { create } from 'zustand';
import type { User } from '../types/index';
import { STORAGE_KEYS } from '../constants';

interface AuthState {
  // 状态
  isAuthenticated: boolean;
  token: string | null;
  user: User | null;
  loading: boolean;

  // 操作
  setToken: (token: string) => void;
  setUser: (user: User) => void;
  login: (token: string, user: User) => void;
  logout: () => void;
  setLoading: (loading: boolean) => void;
  updateUser: (userData: Partial<User>) => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  // 初始状态
  isAuthenticated: false,
  token: null,
  user: null,
  loading: false,

  // 设置token
  setToken: (token: string) => {
    set({ token, isAuthenticated: !!token });
    if (token) {
      localStorage.setItem(STORAGE_KEYS.TOKEN, token);
    } else {
      localStorage.removeItem(STORAGE_KEYS.TOKEN);
    }
  },

  // 设置用户信息
  setUser: (user: User) => {
    set({ user, isAuthenticated: !!user });
    if (user) {
      localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(user));
    } else {
      localStorage.removeItem(STORAGE_KEYS.USER_INFO);
    }
  },

  // 登录
  login: (token: string, user: User) => {
    set({
      token,
      user,
      isAuthenticated: true,
      loading: false
    });
    localStorage.setItem(STORAGE_KEYS.TOKEN, token);
    localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(user));
  },

  // 登出
  logout: () => {
    set({
      token: null,
      user: null,
      isAuthenticated: false,
      loading: false
    });
    localStorage.removeItem(STORAGE_KEYS.TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER_INFO);
  },

  // 设置加载状态
  setLoading: (loading: boolean) => {
    set({ loading });
  },

  // 更新用户信息
  updateUser: (userData: Partial<User>) => {
    const currentUser = get().user;
    if (currentUser) {
      const updatedUser = { ...currentUser, ...userData };
      set({ user: updatedUser });
      localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(updatedUser));
    }
  },
}));
