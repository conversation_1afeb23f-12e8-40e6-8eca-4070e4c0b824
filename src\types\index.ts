// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  role: UserRole;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserRole {
  id: string;
  name: string;
  permissions: Permission[];
}

export interface Permission {
  id: string;
  name: string;
  code: string;
  type: 'menu' | 'action';
}

// 图书相关类型
export interface Book {
  id: string;
  title: string;
  author: string;
  isbn: string;
  category: string;
  publisher: string;
  publishDate: string;
  totalCopies: number;
  availableCopies: number;
  location: string;
  description?: string;
  coverUrl?: string;
  createdAt: string;
  updatedAt: string;
}

// 借还书记录类型
export interface BorrowRecord {
  id: string;
  bookId: string;
  bookTitle: string;
  userId: string;
  userName: string;
  borrowDate: string;
  dueDate: string;
  returnDate?: string;
  status: 'borrowed' | 'returned' | 'overdue';
  renewCount: number;
  fine?: number;
}

// 机器人状态类型
export interface RobotStatus {
  id: string;
  name: string;
  type: 'sorting' | 'delivery' | 'maintenance';
  status: 'online' | 'offline' | 'error' | 'maintenance';
  location: string;
  battery: number;
  lastUpdate: string;
  workingHours: number;
  tasksCompleted: number;
}

// 故障报告类型
export interface FaultReport {
  id: string;
  robotId: string;
  robotName: string;
  faultType: 'hardware' | 'software' | 'network' | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  reportTime: string;
  status: 'pending' | 'processing' | 'resolved' | 'closed';
  assignedTo?: string;
  resolvedTime?: string;
  solution?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

export interface PaginatedResponse<T> {
  list: T[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
}

// 路由相关类型
export interface RouteConfig {
  path: string;
  component: React.ComponentType;
  exact?: boolean;
  meta?: {
    title: string;
    requireAuth?: boolean;
    permissions?: string[];
    icon?: string;
    hideInMenu?: boolean;
  };
  children?: RouteConfig[];
}

// 菜单类型
export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  path?: string;
  children?: MenuItem[];
  permissions?: string[];
}
