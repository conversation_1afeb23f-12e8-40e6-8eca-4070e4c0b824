# React Router v7 升级指南

## 🚀 升级概述

本项目已成功升级到 React Router v7，使用最新的路由语法和特性。

## 📦 依赖变更

### 之前 (v6)
```json
{
  "dependencies": {
    "react-router-dom": "^7.6.3"
  }
}
```

### 现在 (v7)
```json
{
  "dependencies": {
    "react-router": "^7.6.3"
  }
}
```

**原因**: React Router v7 将所有功能合并到 `react-router` 包中，`react-router-dom` 现在只是一个兼容性包装器。

## 🔄 导入语句更新

### 之前
```typescript
import { useNavigate, useLocation } from 'react-router-dom';
import { createBrowserRouter, Navigate } from 'react-router-dom';
```

### 现在
```typescript
import { useNavigate, useLocation } from 'react-router';
import { createBrowserRouter, Navigate, redirect } from 'react-router';
```

## 🆕 新特性使用

### 1. 路由保护函数 (Route Guards)

```typescript
// 新增：路由保护函数
const requireAuth = () => {
  const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
  const userInfo = localStorage.getItem(STORAGE_KEYS.USER_INFO);
  
  if (!token || !userInfo) {
    throw redirect(ROUTES.LOGIN);
  }
  
  try {
    const user = JSON.parse(userInfo);
    return { token, user };
  } catch {
    localStorage.removeItem(STORAGE_KEYS.TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER_INFO);
    throw redirect(ROUTES.LOGIN);
  }
};

// 检查是否已登录，如果已登录则重定向
const redirectIfAuthenticated = () => {
  const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
  if (token) {
    throw redirect(ROUTES.DASHBOARD);
  }
  return null;
};
```

### 2. 路由配置中使用 Loader

```typescript
export const router = createBrowserRouter([
  {
    path: ROUTES.LOGIN,
    element: <LazyWrapper><Login /></LazyWrapper>,
    loader: redirectIfAuthenticated, // 如果已登录则重定向
  },
  {
    path: '/',
    element: <AuthGuard><Layout /></AuthGuard>,
    loader: requireAuth, // 需要认证才能访问
    children: [
      // ... 子路由
    ],
  },
]);
```

### 3. 增强的导航功能

```typescript
// 之前
navigate('/dashboard');

// 现在 - 带状态传递
navigate(ROUTES.DASHBOARD, {
  replace: true,
  state: { 
    from: window.location.pathname,
    success: '操作成功'
  }
});
```

## 📁 更新的文件列表

### 核心路由文件
- ✅ `src/router/index.tsx` - 主路由配置
- ✅ `src/router/AuthGuard.tsx` - 认证守卫
- ✅ `src/main.tsx` - 应用入口

### 布局组件
- ✅ `src/layout/index.tsx` - 主布局
- ✅ `src/layout/Header/index.tsx` - 头部导航
- ✅ `src/layout/Sidebar/index.tsx` - 侧边栏
- ✅ `src/layout/Breadcrumb/index.tsx` - 面包屑

### 页面组件
- ✅ `src/pages/Login/index.tsx` - 登录页
- ✅ `src/pages/Books/BookList/index.tsx` - 图书列表
- ✅ `src/pages/Books/BookAdd/index.tsx` - 添加图书
- ✅ `src/pages/Books/BookEdit/index.tsx` - 编辑图书

### 通用组件
- ✅ `src/components/PermissionGuard/index.tsx` - 权限守卫

## 🎯 主要改进

### 1. 更好的类型安全
```typescript
// 使用 React Router v7 的类型定义
const { id } = useParams<{ id: string }>();
const location = useLocation();
const navigate = useNavigate();
```

### 2. 状态传递优化
```typescript
// 登录成功后传递状态
navigate(from, { 
  replace: true,
  state: { from: location.pathname }
});

// 错误处理时传递错误信息
navigate(ROUTES.BOOK_LIST, {
  replace: true,
  state: { error: '获取图书详情失败' }
});
```

### 3. 路由级别的数据加载
```typescript
// 使用 loader 在路由级别进行数据预加载和权限检查
{
  path: ROUTES.LOGIN,
  loader: redirectIfAuthenticated,
  element: <Login />
}
```

## 🔧 开发体验提升

1. **更简洁的依赖**: 只需要一个 `react-router` 包
2. **更好的性能**: 路由级别的数据加载和缓存
3. **更强的类型安全**: 完整的 TypeScript 支持
4. **更灵活的导航**: 支持状态传递和复杂的导航逻辑

## 🚀 启动项目

```bash
npm run dev
```

项目现在运行在: http://localhost:5175/

## 📝 注意事项

1. 所有路由相关的导入都已更新为从 `react-router` 导入
2. 使用了 React Router v7 的新特性如 `loader` 和 `redirect`
3. 增强了导航时的状态传递功能
4. 保持了向后兼容性，现有功能不受影响

## 🎉 升级完成

项目已成功升级到 React Router v7，享受最新的路由特性和更好的开发体验！
