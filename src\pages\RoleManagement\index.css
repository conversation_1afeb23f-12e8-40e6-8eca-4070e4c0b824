.role-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin-bottom: 8px;
  color: #1890ff;
}

/* 表格样式优化 */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 标签样式 */
.ant-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 操作按钮样式 */
.ant-btn-link {
  padding: 4px 8px;
  height: auto;
}

.ant-btn-link:hover {
  background: rgba(24, 144, 255, 0.1);
  border-radius: 4px;
}

/* 搜索区域样式 */
.ant-input-affix-wrapper {
  border-radius: 6px;
}

.ant-select-selector {
  border-radius: 6px;
}

/* 模态框样式 */
.ant-modal-header {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-title {
  font-weight: 600;
  color: #262626;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input-password,
.ant-select-selector,
.ant-input {
  border-radius: 6px;
}

.ant-input:focus,
.ant-input-focused,
.ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 权限树样式 */
.ant-tree {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.ant-tree-node-content-wrapper {
  border-radius: 4px;
}

.ant-tree-node-content-wrapper:hover {
  background: rgba(24, 144, 255, 0.1);
}

.ant-tree-node-selected .ant-tree-node-content-wrapper {
  background: rgba(24, 144, 255, 0.2);
}

.ant-tree-checkbox {
  margin-right: 8px;
}

/* 权限详情模态框样式 */
.ant-modal-info .ant-modal-content {
  border-radius: 8px;
}

.ant-modal-info .ant-modal-body {
  padding: 24px;
}

/* 分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

.ant-pagination-total-text {
  color: #666;
}

/* 卡片样式 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
}

.ant-card-body {
  padding: 24px;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .role-management {
    padding: 16px;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .ant-card-body {
    padding: 16px;
  }
  
  /* 移动端表格滚动 */
  .ant-table-wrapper {
    overflow-x: auto;
  }
  
  /* 移动端搜索区域 */
  .ant-row {
    margin-bottom: 8px;
  }
  
  .ant-col {
    margin-bottom: 8px;
  }
  
  /* 移动端权限树 */
  .ant-tree {
    padding: 12px;
    max-height: 200px;
    overflow-y: auto;
  }
}

@media (max-width: 576px) {
  .role-management {
    padding: 12px;
  }
  
  .page-header {
    padding: 12px;
  }
  
  .page-header h2 {
    font-size: 18px;
  }
  
  .ant-card-body {
    padding: 12px;
  }
  
  /* 移动端模态框 */
  .ant-modal {
    margin: 0;
    max-width: 100vw;
  }
  
  .ant-modal-content {
    border-radius: 0;
  }
}

/* 状态标签颜色优化 */
.ant-tag-green {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.ant-tag-red {
  background: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

.ant-tag-blue {
  background: #f0f5ff;
  border-color: #adc6ff;
  color: #1890ff;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  background: rgba(0, 0, 0, 0.85);
  border-radius: 4px;
}

/* 确认弹框样式 */
.ant-popconfirm-inner {
  border-radius: 6px;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

.ant-empty-description {
  color: #999;
}

/* 加载状态样式 */
.ant-spin-container {
  position: relative;
}

.ant-spin-blur {
  opacity: 0.5;
  pointer-events: none;
}

/* 表格行选择样式 */
.ant-table-row-selected > td {
  background: #e6f7ff;
}

/* 权限分类标题样式 */
.permission-category-title {
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

/* 权限项样式 */
.permission-item {
  margin-bottom: 4px;
  padding: 4px 8px;
  background: #f9f9f9;
  border-radius: 4px;
  display: inline-block;
  margin-right: 8px;
}

/* 文本域样式 */
.ant-input {
  border-radius: 6px;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 穿梭框样式优化 */
.ant-transfer {
  display: flex;
  align-items: center;
  gap: 16px;
}

.ant-transfer-list {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  overflow: hidden;
}

.ant-transfer-list-header {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
  font-weight: 500;
  color: #262626;
}

.ant-transfer-list-body {
  background: #fff;
}

.ant-transfer-list-content {
  max-height: 300px;
  overflow-y: auto;
}

.ant-transfer-list-content-item {
  padding: 8px 16px;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.2s;
  cursor: pointer;
}

.ant-transfer-list-content-item:hover {
  background: #f5f5f5;
}

.ant-transfer-list-content-item-checked {
  background: #e6f7ff;
}

.ant-transfer-operation {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ant-transfer-operation .ant-btn {
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

/* 权限模式切换样式 */
.permission-mode-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.permission-mode-selector .ant-select {
  min-width: 100px;
}

/* 树形选择器样式优化 */
.permission-tree-container {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background: #fff;
  overflow: hidden;
}

.permission-tree-container .ant-tree {
  background: transparent;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.permission-tree-container .ant-tree-treenode {
  padding: 2px 0;
}

.permission-tree-container .ant-tree-node-content-wrapper {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.permission-tree-container .ant-tree-node-content-wrapper:hover {
  background: #f0f5ff;
}

.permission-tree-container .ant-tree-node-selected .ant-tree-node-content-wrapper {
  background: #e6f7ff;
}

.permission-tree-container .ant-tree-checkbox {
  margin-right: 8px;
}

.permission-tree-container .ant-tree-title {
  font-size: 14px;
  color: #262626;
}

/* 父节点样式 */
.permission-tree-container .ant-tree-treenode-switcher-open .ant-tree-title,
.permission-tree-container .ant-tree-treenode-switcher-close .ant-tree-title {
  font-weight: 500;
  color: #1890ff;
}
