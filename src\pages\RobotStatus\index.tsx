import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Typography,
  Tag,
  Button,
  Progress,
  Statistic,
  Space,
  Modal,
  message,
  Tooltip,
  Badge,
} from 'antd';
import {
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  RedoOutlined,
  RobotOutlined,
  <PERSON>boltOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import type { RobotStatus as RobotStatusType } from '../../types/index';
import {
  getRobotList,
  getRobotStatistics,
  controlRobot
} from '../../services/robotService';
import { ROBOT_STATUS, ROBOT_TYPES } from '../../constants';
import { usePermission } from '../../hooks/usePermission';
import PermissionGuard from '../../components/PermissionGuard';
import { PERMISSIONS } from '../../constants';

const { Title, Text } = Typography;

interface Statistics {
  total: number;
  online: number;
  offline: number;
  error: number;
  maintenance: number;
  averageBattery: number;
  totalTasks: number;
}

const RobotStatus: React.FC = () => {
  const { hasPermission } = usePermission();

  const [robots, setRobots] = useState<RobotStatusType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [statistics, setStatistics] = useState<Statistics>({
    total: 0,
    online: 0,
    offline: 0,
    error: 0,
    maintenance: 0,
    averageBattery: 0,
    totalTasks: 0,
  });
  const [autoRefresh, setAutoRefresh] = useState<boolean>(true);

  // 获取机器人列表
  const fetchRobots = async () => {
    setLoading(true);
    try {
      const response = await getRobotList();
      if (response.success) {
        setRobots(response.data);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('获取机器人状态失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      const response = await getRobotStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  useEffect(() => {
    fetchRobots();
    fetchStatistics();
  }, []);

  // 自动刷新
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(() => {
        fetchRobots();
        fetchStatistics();
      }, 30000); // 30秒刷新一次
    }
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [autoRefresh]);

  // 处理机器人控制
  const handleControl = async (robot: RobotStatusType, action: 'start' | 'stop' | 'restart') => {
    const actionText = action === 'start' ? '启动' : action === 'stop' ? '停止' : '重启';

    Modal.confirm({
      title: `确认${actionText}`,
      content: `确定要${actionText}机器人"${robot.name}"吗？`,
      onOk: async () => {
        try {
          const response = await controlRobot(robot.id, action);
          if (response.success) {
            message.success(`${actionText}成功`);
            fetchRobots();
            fetchStatistics();
          } else {
            message.error(response.message);
          }
        } catch (error) {
          message.error(`${actionText}失败`);
        }
      },
    });
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig = {
      [ROBOT_STATUS.ONLINE]: { color: 'green', text: '在线' },
      [ROBOT_STATUS.OFFLINE]: { color: 'default', text: '离线' },
      [ROBOT_STATUS.ERROR]: { color: 'red', text: '故障' },
      [ROBOT_STATUS.MAINTENANCE]: { color: 'orange', text: '维护中' },
    };

    const config = statusConfig[status] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取机器人类型标签
  const getTypeTag = (type: string) => {
    const typeConfig = {
      [ROBOT_TYPES.SORTING]: { color: 'blue', text: '分拣机器人' },
      [ROBOT_TYPES.DELIVERY]: { color: 'purple', text: '配送机器人' },
      [ROBOT_TYPES.MAINTENANCE]: { color: 'cyan', text: '维护机器人' },
    };

    const config = typeConfig[type] || { color: 'default', text: '未知类型' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取电池颜色
  const getBatteryColor = (battery: number) => {
    if (battery > 60) return '#52c41a';
    if (battery > 30) return '#faad14';
    return '#f5222d';
  };

  // 获取最后更新时间显示
  const getLastUpdateText = (lastUpdate: string) => {
    const diff = dayjs().diff(dayjs(lastUpdate), 'minute');
    if (diff < 1) return '刚刚';
    if (diff < 60) return `${diff}分钟前`;
    const hours = Math.floor(diff / 60);
    if (hours < 24) return `${hours}小时前`;
    const days = Math.floor(hours / 24);
    return `${days}天前`;
  };

  return (
    <PermissionGuard permission={PERMISSIONS.ROBOT_VIEW}>
      <div>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>机器人状态监控</Title>
          <Space>
            <Button
              type={autoRefresh ? 'primary' : 'default'}
              onClick={() => setAutoRefresh(!autoRefresh)}
            >
              {autoRefresh ? '停止自动刷新' : '开启自动刷新'}
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                fetchRobots();
                fetchStatistics();
              }}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="机器人总数"
                value={statistics.total}
                prefix={<RobotOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="在线"
                value={statistics.online}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="离线"
                value={statistics.offline}
                valueStyle={{ color: '#8c8c8c' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="故障"
                value={statistics.error}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="平均电量"
                value={statistics.averageBattery}
                suffix="%"
                valueStyle={{ color: getBatteryColor(statistics.averageBattery) }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="完成任务"
                value={statistics.totalTasks}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 机器人卡片列表 */}
        <Row gutter={[16, 16]}>
          {robots.map((robot) => (
            <Col xs={24} sm={12} md={8} lg={6} key={robot.id}>
              <Card
                size="small"
                title={
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span>{robot.name}</span>
                    <Badge
                      status={robot.status === ROBOT_STATUS.ONLINE ? 'processing' :
                             robot.status === ROBOT_STATUS.ERROR ? 'error' : 'default'}
                    />
                  </div>
                }
                extra={getStatusTag(robot.status)}
                actions={
                  hasPermission(PERMISSIONS.ROBOT_CONTROL) ? [
                    robot.status === ROBOT_STATUS.OFFLINE && (
                      <Tooltip title="启动" key="start">
                        <PlayCircleOutlined
                          onClick={() => handleControl(robot, 'start')}
                          style={{ color: '#52c41a' }}
                        />
                      </Tooltip>
                    ),
                    robot.status === ROBOT_STATUS.ONLINE && (
                      <Tooltip title="停止" key="stop">
                        <PauseCircleOutlined
                          onClick={() => handleControl(robot, 'stop')}
                          style={{ color: '#faad14' }}
                        />
                      </Tooltip>
                    ),
                    robot.status === ROBOT_STATUS.ERROR && (
                      <Tooltip title="重启" key="restart">
                        <RedoOutlined
                          onClick={() => handleControl(robot, 'restart')}
                          style={{ color: '#1890ff' }}
                        />
                      </Tooltip>
                    ),
                  ].filter(Boolean) : []
                }
              >
                <div style={{ marginBottom: 8 }}>
                  {getTypeTag(robot.type)}
                </div>

                <div style={{ marginBottom: 12 }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                    <ThunderboltOutlined style={{ color: getBatteryColor(robot.battery) }} />
                    <Text style={{ marginLeft: 4 }}>电量</Text>
                  </div>
                  <Progress
                    percent={robot.battery}
                    size="small"
                    strokeColor={getBatteryColor(robot.battery)}
                    format={(percent) => `${percent}%`}
                  />
                </div>

                <div style={{ marginBottom: 8 }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                    <EnvironmentOutlined style={{ color: '#1890ff' }} />
                    <Text style={{ marginLeft: 4 }}>位置：{robot.location}</Text>
                  </div>
                </div>

                <div style={{ marginBottom: 8 }}>
                  <Text type="secondary">工作时长：{robot.workingHours}小时</Text>
                </div>

                <div style={{ marginBottom: 8 }}>
                  <Text type="secondary">完成任务：{robot.tasksCompleted}个</Text>
                </div>

                <div>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <ClockCircleOutlined style={{ color: '#8c8c8c' }} />
                    <Text type="secondary" style={{ marginLeft: 4, fontSize: '12px' }}>
                      更新：{getLastUpdateText(robot.lastUpdate)}
                    </Text>
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    </PermissionGuard>
  );
};

export default RobotStatus;
