import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Avatar,
  Upload,
  Space,
  Typography,
  Row,
  Col,
  Divider,
  message,
  Modal,
} from 'antd';
import {
  UserOutlined,
  CameraOutlined,
  EditOutlined,
  LockOutlined,
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { useAuthStore } from '../../store/authStore';
import defaultAvatar from '../../assets/soybean.jpg';
import './index.css';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface ProfileFormData {
  username: string;
  email: string;
  phone?: string;
  bio?: string;
}

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const Profile: React.FC = () => {
  const { user, updateUser } = useAuthStore();
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState(user?.avatar || defaultAvatar);

  const handleProfileUpdate = async (values: ProfileFormData) => {
    setLoading(true);
    try {
      // 模拟更新用户信息
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      updateUser({
        ...user!,
        username: values.username,
        email: values.email,
        // 这里可以添加更多字段
      });
      
      message.success('个人信息更新成功！');
    } catch (error) {
      message.error('更新失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (values: PasswordFormData) => {
    setLoading(true);
    try {
      // 模拟密码修改
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('密码修改成功！');
      setPasswordModalVisible(false);
      passwordForm.resetFields();
    } catch (error) {
      message.error('密码修改失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const uploadProps: UploadProps = {
    name: 'avatar',
    listType: 'picture-card',
    className: 'avatar-uploader',
    showUploadList: false,
    beforeUpload: (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error('只能上传 JPG/PNG 格式的图片!');
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('图片大小不能超过 2MB!');
        return false;
      }
      
      // 模拟上传
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatarUrl(e.target?.result as string);
        message.success('头像上传成功！');
      };
      reader.readAsDataURL(file);
      
      return false; // 阻止自动上传
    },
  };

  return (
    <div className="profile-container">
      <div className="profile-header">
        <Title level={2}>
          <UserOutlined /> 个人信息
        </Title>
        <Text type="secondary">管理您的个人资料和账户设置</Text>
      </div>

      <Row gutter={24}>
        <Col xs={24} lg={8}>
          <Card className="avatar-card">
            <div className="avatar-section">
              <Avatar
                size={100}
                src={avatarUrl}
                icon={<UserOutlined />}
                className="profile-avatar"
              />
              <Upload {...uploadProps}>
                <Button
                  type="primary"
                  icon={<CameraOutlined />}
                  size="small"
                  className="upload-btn"
                >
                  更换头像
                </Button>
              </Upload>
            </div>

            <Divider />

            <div className="user-info">
              <Row gutter={[8, 12]}>
                <Col span={8}>
                  <div className="info-item-horizontal">
                    <UserOutlined className="info-icon" />
                    <div className="info-content">
                      <Text type="secondary" className="info-label">用户名</Text>
                      <Text strong className="info-value">{user?.username || 'librarian'}</Text>
                    </div>
                  </div>
                </Col>

                <Col span={8}>
                  <div className="info-item-horizontal">
                    <MailOutlined className="info-icon" />
                    <div className="info-content">
                      <Text type="secondary" className="info-label">邮箱</Text>
                      <Text strong className="info-value">{user?.email || '<EMAIL>'}</Text>
                    </div>
                  </div>
                </Col>

                <Col span={8}>
                  <div className="info-item-horizontal">
                    <CalendarOutlined className="info-icon" />
                    <div className="info-content">
                      <Text type="secondary" className="info-label">注册时间</Text>
                      <Text strong className="info-value">{user?.createdAt || '2024-01-01T00:00:00Z'}</Text>
                    </div>
                  </div>
                </Col>
              </Row>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={16}>
          <Card
            title={<><EditOutlined /> 编辑资料</>}
            className="profile-form-card"
          >
            <Form
              form={profileForm}
              layout="vertical"
              initialValues={{
                username: user?.username,
                email: user?.email,
                phone: '',
                bio: '',
              }}
              onFinish={handleProfileUpdate}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="用户名"
                    name="username"
                    rules={[
                      { required: true, message: '请输入用户名' },
                      { min: 2, message: '用户名至少2个字符' }
                    ]}
                  >
                    <Input prefix={<UserOutlined />} placeholder="请输入用户名" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="邮箱"
                    name="email"
                    rules={[
                      { required: true, message: '请输入邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                  >
                    <Input prefix={<MailOutlined />} placeholder="请输入邮箱" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="手机号"
                name="phone"
                rules={[
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                ]}
              >
                <Input prefix={<PhoneOutlined />} placeholder="请输入手机号" />
              </Form.Item>

              <Form.Item
                label="个人简介"
                name="bio"
              >
                <TextArea
                  rows={4}
                  placeholder="介绍一下自己..."
                  maxLength={200}
                  showCount
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" loading={loading}>
                    保存修改
                  </Button>
                  <Button
                    icon={<LockOutlined />}
                    onClick={() => setPasswordModalVisible(true)}
                  >
                    修改密码
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>

      {/* 修改密码模态框 */}
      <Modal
        title={<><LockOutlined /> 修改密码</>}
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false);
          passwordForm.resetFields();
        }}
        footer={null}
        width={400}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handlePasswordChange}
        >
          <Form.Item
            label="当前密码"
            name="currentPassword"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password placeholder="请输入当前密码" />
          </Form.Item>
          
          <Form.Item
            label="新密码"
            name="newPassword"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>
          
          <Form.Item
            label="确认新密码"
            name="confirmPassword"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password placeholder="请再次输入新密码" />
          </Form.Item>
          
          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setPasswordModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                确认修改
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Profile;
