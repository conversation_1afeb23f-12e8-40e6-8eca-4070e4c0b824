import React from 'react';
import { Row, Col, Card, Statistic, Typography, Space } from 'antd';
import {
  BookOutlined,
  UserOutlined,
  RobotOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';

const { Title } = Typography;

const Dashboard: React.FC = () => {
  return (
    <div>
      <Title level={2}>仪表盘</Title>
      
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="图书总数"
              value={1234}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="在线用户"
              value={56}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="机器人状态"
              value={8}
              suffix="/ 10"
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="待处理故障"
              value={3}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} lg={12}>
          <Card title="最近借书记录" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>《JavaScript高级程序设计》 - 张三</div>
              <div>《React实战》 - 李四</div>
              <div>《TypeScript入门》 - 王五</div>
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} lg={12}>
          <Card title="系统通知" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>机器人维护计划已安排</div>
              <div>新书入库完成</div>
              <div>系统更新通知</div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
