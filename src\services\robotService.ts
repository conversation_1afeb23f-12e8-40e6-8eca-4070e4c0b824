import type { RobotStatus, ApiResponse } from '../types/index';
import { ROBOT_STATUS, ROBOT_TYPES } from '../constants';

// 模拟机器人状态数据
const mockRobots: RobotStatus[] = [
  {
    id: 'robot-001',
    name: '分拣机器人-01',
    type: ROBOT_TYPES.SORTING,
    status: ROBOT_STATUS.ONLINE,
    location: 'A区-分拣中心',
    battery: 85,
    lastUpdate: new Date().toISOString(),
    workingHours: 6.5,
    tasksCompleted: 127,
  },
  {
    id: 'robot-002',
    name: '分拣机器人-02',
    type: ROBOT_TYPES.SORTING,
    status: ROBOT_STATUS.ONLINE,
    location: 'B区-分拣中心',
    battery: 92,
    lastUpdate: new Date().toISOString(),
    workingHours: 5.2,
    tasksCompleted: 98,
  },
  {
    id: 'robot-003',
    name: '配送机器人-01',
    type: ROBOT_TYPES.DELIVERY,
    status: ROBOT_STATUS.ONLINE,
    location: '2楼-阅读区',
    battery: 67,
    lastUpdate: new Date().toISOString(),
    workingHours: 7.8,
    tasksCompleted: 45,
  },
  {
    id: 'robot-004',
    name: '配送机器人-02',
    type: ROBOT_TYPES.DELIVERY,
    status: ROBOT_STATUS.ERROR,
    location: '1楼-大厅',
    battery: 23,
    lastUpdate: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15分钟前
    workingHours: 3.1,
    tasksCompleted: 28,
  },
  {
    id: 'robot-005',
    name: '维护机器人-01',
    type: ROBOT_TYPES.MAINTENANCE,
    status: ROBOT_STATUS.MAINTENANCE,
    location: '地下室-维护区',
    battery: 100,
    lastUpdate: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2小时前
    workingHours: 0,
    tasksCompleted: 0,
  },
  {
    id: 'robot-006',
    name: '分拣机器人-03',
    type: ROBOT_TYPES.SORTING,
    status: ROBOT_STATUS.OFFLINE,
    location: 'C区-分拣中心',
    battery: 0,
    lastUpdate: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4小时前
    workingHours: 8.5,
    tasksCompleted: 156,
  },
  {
    id: 'robot-007',
    name: '配送机器人-03',
    type: ROBOT_TYPES.DELIVERY,
    status: ROBOT_STATUS.ONLINE,
    location: '3楼-研究区',
    battery: 78,
    lastUpdate: new Date().toISOString(),
    workingHours: 4.3,
    tasksCompleted: 32,
  },
  {
    id: 'robot-008',
    name: '分拣机器人-04',
    type: ROBOT_TYPES.SORTING,
    status: ROBOT_STATUS.ONLINE,
    location: 'D区-分拣中心',
    battery: 91,
    lastUpdate: new Date().toISOString(),
    workingHours: 6.8,
    tasksCompleted: 143,
  },
];

// 获取机器人状态列表
export const getRobotList = async (): Promise<ApiResponse<RobotStatus[]>> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 800));

  // 模拟实时更新部分机器人状态
  const updatedRobots = mockRobots.map(robot => {
    if (robot.status === ROBOT_STATUS.ONLINE) {
      // 随机更新电池电量和任务完成数
      const batteryChange = Math.random() * 4 - 2; // -2 到 +2
      const newBattery = Math.max(0, Math.min(100, robot.battery + batteryChange));
      
      const taskChange = Math.random() > 0.7 ? 1 : 0; // 30%概率完成新任务
      
      return {
        ...robot,
        battery: Math.round(newBattery),
        tasksCompleted: robot.tasksCompleted + taskChange,
        lastUpdate: new Date().toISOString(),
      };
    }
    return robot;
  });

  return {
    code: 200,
    message: '获取成功',
    data: updatedRobots,
    success: true,
  };
};

// 获取机器人详情
export const getRobotDetail = async (id: string): Promise<ApiResponse<RobotStatus>> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  const robot = mockRobots.find(r => r.id === id);
  if (!robot) {
    return {
      code: 404,
      message: '机器人不存在',
      data: null,
      success: false,
    };
  }

  return {
    code: 200,
    message: '获取成功',
    data: robot,
    success: true,
  };
};

// 获取机器人统计数据
export const getRobotStatistics = async (): Promise<ApiResponse<{
  total: number;
  online: number;
  offline: number;
  error: number;
  maintenance: number;
  averageBattery: number;
  totalTasks: number;
}>> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  const total = mockRobots.length;
  const online = mockRobots.filter(r => r.status === ROBOT_STATUS.ONLINE).length;
  const offline = mockRobots.filter(r => r.status === ROBOT_STATUS.OFFLINE).length;
  const error = mockRobots.filter(r => r.status === ROBOT_STATUS.ERROR).length;
  const maintenance = mockRobots.filter(r => r.status === ROBOT_STATUS.MAINTENANCE).length;
  
  const totalBattery = mockRobots.reduce((sum, r) => sum + r.battery, 0);
  const averageBattery = Math.round(totalBattery / total);
  
  const totalTasks = mockRobots.reduce((sum, r) => sum + r.tasksCompleted, 0);

  return {
    code: 200,
    message: '获取成功',
    data: {
      total,
      online,
      offline,
      error,
      maintenance,
      averageBattery,
      totalTasks,
    },
    success: true,
  };
};

// 控制机器人（启动/停止/重启）
export const controlRobot = async (
  id: string, 
  action: 'start' | 'stop' | 'restart'
): Promise<ApiResponse<RobotStatus>> => {
  await new Promise(resolve => setTimeout(resolve, 1000));

  const robotIndex = mockRobots.findIndex(r => r.id === id);
  if (robotIndex === -1) {
    return {
      code: 404,
      message: '机器人不存在',
      data: null,
      success: false,
    };
  }

  const robot = mockRobots[robotIndex];
  let newStatus = robot.status;

  switch (action) {
    case 'start':
      if (robot.status === ROBOT_STATUS.OFFLINE) {
        newStatus = ROBOT_STATUS.ONLINE;
      } else {
        return {
          code: 400,
          message: '机器人当前状态无法启动',
          data: null,
          success: false,
        };
      }
      break;
    case 'stop':
      if (robot.status === ROBOT_STATUS.ONLINE) {
        newStatus = ROBOT_STATUS.OFFLINE;
      } else {
        return {
          code: 400,
          message: '机器人当前状态无法停止',
          data: null,
          success: false,
        };
      }
      break;
    case 'restart':
      if (robot.status === ROBOT_STATUS.ERROR) {
        newStatus = ROBOT_STATUS.ONLINE;
      } else {
        return {
          code: 400,
          message: '只有故障状态的机器人才能重启',
          data: null,
          success: false,
        };
      }
      break;
  }

  const updatedRobot = {
    ...robot,
    status: newStatus,
    lastUpdate: new Date().toISOString(),
  };

  mockRobots[robotIndex] = updatedRobot;

  return {
    code: 200,
    message: `${action === 'start' ? '启动' : action === 'stop' ? '停止' : '重启'}成功`,
    data: updatedRobot,
    success: true,
  };
};

// 获取机器人历史状态（模拟数据）
export const getRobotHistory = async (
  id: string,
  hours: number = 24
): Promise<ApiResponse<Array<{
  timestamp: string;
  status: string;
  battery: number;
  location: string;
}>>> => {
  await new Promise(resolve => setTimeout(resolve, 600));

  // 生成模拟历史数据
  const history = [];
  const now = new Date();
  
  for (let i = hours; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
    history.push({
      timestamp: timestamp.toISOString(),
      status: i < 2 ? ROBOT_STATUS.ERROR : ROBOT_STATUS.ONLINE,
      battery: Math.max(20, 100 - i * 2 + Math.random() * 10),
      location: `位置-${Math.floor(Math.random() * 5) + 1}`,
    });
  }

  return {
    code: 200,
    message: '获取成功',
    data: history,
    success: true,
  };
};
