import React, { useState } from 'react';
import { Layout, Button, Dropdown, Avatar, Space, Badge, Typography, Divider } from 'antd';
import type { MenuProps } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  CheckOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router';
import { useAuthStore } from '../../store/authStore';
import { ROUTES } from '../../constants';
import defaultAvatar from '../../assets/soybean.jpg';
import './index.css';

const { Header: AntHeader } = Layout;
const { Text } = Typography;

interface HeaderProps {
  collapsed: boolean;
  onToggleCollapsed: () => void;
}

interface Notification {
  id: string;
  title: string;
  content: string;
  type: 'fault' | 'maintenance' | 'info';
  time: string;
}

const Header: React.FC<HeaderProps> = ({ collapsed, onToggleCollapsed }) => {
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();

  // 通知状态管理
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      title: '机器人故障报告',
      content: '分拣机器人-01 出现硬件故障',
      type: 'fault',
      time: '5分钟前'
    },
    {
      id: '2',
      title: '系统维护通知',
      content: '系统将于今晚22:00进行维护',
      type: 'maintenance',
      time: '1小时前'
    }
  ]);

  // 通知操作函数
  const handleNotificationClick = (notificationId: string, e: React.MouseEvent) => {
    // 阻止事件冒泡，防止关闭下拉菜单
    e.stopPropagation();
    e.preventDefault();
    // 点击通知后移除该通知
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  const handleMarkAllRead = () => {
    // 标记全部已读（清空所有通知）
    setNotifications([]);
  };

  const handleClearAll = () => {
    // 清空所有通知
    setNotifications([]);
  };

  // 用户下拉菜单 - 使用 React Router v7 的新导航方式
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
      onClick: () => {
        navigate(ROUTES.PROFILE, {
          state: { from: window.location.pathname }
        });
      },
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      onClick: () => {
        navigate(ROUTES.SETTINGS, {
          state: { from: window.location.pathname }
        });
      },
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => {
        logout();
        navigate(ROUTES.LOGIN, {
          replace: true,
          state: { loggedOut: true }
        });
      },
    },
  ];

  // 通知下拉菜单
  const notificationMenuItems: MenuProps['items'] = [
    // 通知列表
    ...notifications.map(notification => ({
      key: notification.id,
      label: (
        <div
          style={{
            padding: '8px 0',
            borderBottom: '1px solid #f0f0f0',
            maxWidth: '280px'
          }}
        >
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <div style={{ flex: 1 }}>
              <Text strong style={{ fontSize: '13px' }}>{notification.title}</Text>
              <br />
              <Text type="secondary" style={{ fontSize: '12px', lineHeight: '1.4' }}>
                {notification.content}
              </Text>
              <br />
              <Text type="secondary" style={{ fontSize: '11px' }}>
                {notification.time}
              </Text>
            </div>
            <Button
              type="link"
              size="small"
              icon={<CheckOutlined />}
              onClick={(e) => handleNotificationClick(notification.id, e)}
              style={{
                padding: '0 4px',
                height: 'auto',
                fontSize: '11px',
                color: '#1890ff'
              }}
            >
              已读
            </Button>
          </div>
        </div>
      ),
    })),

    // 如果没有通知，显示空状态
    ...(notifications.length === 0 ? [{
      key: 'empty',
      label: (
        <div style={{ textAlign: 'center', padding: '20px 0', color: '#999' }}>
          <BellOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
          <br />
          暂无新通知
        </div>
      ),
      disabled: true,
    }] : []),

    // 分割线和操作按钮
    ...(notifications.length > 0 ? [
      {
        type: 'divider' as const,
      },
      {
        key: 'actions',
        label: (
          <div style={{ padding: '8px 0' }}>
            <Space split={<Divider type="vertical" />}>
              <Button
                type="link"
                size="small"
                icon={<CheckOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleMarkAllRead();
                }}
                style={{ padding: 0, height: 'auto' }}
              >
                全部已读
              </Button>
              <Button
                type="link"
                size="small"
                icon={<DeleteOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleClearAll();
                }}
                style={{ padding: 0, height: 'auto', color: '#ff4d4f' }}
              >
                清空通知
              </Button>
              <Button
                type="link"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(ROUTES.FAULT_REPORTS, {
                    state: { from: window.location.pathname }
                  });
                }}
                style={{ padding: 0, height: 'auto' }}
              >
                查看全部
              </Button>
            </Space>
          </div>
        ),
        disabled: true,
      }
    ] : [])
  ];

  return (
    <AntHeader className="layout-header">
      <div className="header-left">
        <Button
          type="text"
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={onToggleCollapsed}
          style={{
            fontSize: '16px',
            width: 64,
            height: 64,
          }}
        />
        <div className="header-title">
          <Text strong style={{ fontSize: '18px', color: '#fff' }}>
            图书馆管理系统
          </Text>
        </div>
      </div>

      <div className="header-right">
        <Space size="middle">
          {/* 通知铃铛 */}
          <Dropdown
            menu={{
              items: notificationMenuItems,
              onClick: (e) => {
                // 阻止菜单项点击时关闭下拉菜单
                e.domEvent?.stopPropagation();
              }
            }}
            placement="bottomRight"
            trigger={['click']}
            overlayStyle={{ maxHeight: '400px', overflowY: 'auto' }}
          >
            <Badge count={notifications.length} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ color: '#fff' }}
              />
            </Badge>
          </Dropdown>

          {/* 用户信息 */}
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            trigger={['click']}
          >
            <div className="user-info">
              <Avatar
                size="small"
                icon={<UserOutlined />}
                src={user?.avatar || defaultAvatar}
                style={{ marginRight: '8px' }}
              />
              <Text style={{ color: '#fff' }}>
                {user?.username || '管理员'}
              </Text>
            </div>
          </Dropdown>
        </Space>
      </div>
    </AntHeader>
  );
};

export default Header;
