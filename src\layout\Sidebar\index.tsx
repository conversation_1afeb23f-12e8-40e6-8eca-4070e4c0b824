import React, { useState, useEffect } from 'react';
import { Layout, Menu } from 'antd';
import type { MenuProps } from 'antd';
import {
  DashboardOutlined,
  BookOutlined,
  HistoryOutlined,
  RobotOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  TeamOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router';
import { ROUTES, PERMISSIONS } from '../../constants';
import type { MenuItem } from '../../types/index';
import { usePermission } from '../../hooks/usePermission';
import './index.css';

const { Sider } = Layout;

interface SidebarProps {
  collapsed: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ collapsed }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission } = usePermission();
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [openKeys, setOpenKeys] = useState<string[]>([]);

  // 菜单配置
  const menuItems: MenuItem[] = [
    {
      key: ROUTES.DASHBOARD,
      label: '仪表盘',
      icon: <DashboardOutlined />,
      path: ROUTES.DASHBOARD,
    },
    {
      key: 'books',
      label: '图书管理',
      icon: <BookOutlined />,
      permissions: [PERMISSIONS.BOOK_VIEW],
      children: [
        {
          key: ROUTES.BOOK_LIST,
          label: '图书列表',
          path: ROUTES.BOOK_LIST,
          permissions: [PERMISSIONS.BOOK_VIEW],
        },
        {
          key: ROUTES.BOOK_ADD,
          label: '添加图书',
          path: ROUTES.BOOK_ADD,
          permissions: [PERMISSIONS.BOOK_ADD],
        },
      ],
    },
    {
      key: ROUTES.BORROW_RECORDS,
      label: '借还记录',
      icon: <HistoryOutlined />,
      path: ROUTES.BORROW_RECORDS,
      permissions: [PERMISSIONS.BORROW_VIEW],
    },
    {
      key: ROUTES.ROBOT_STATUS,
      label: '机器人状态',
      icon: <RobotOutlined />,
      path: ROUTES.ROBOT_STATUS,
      permissions: [PERMISSIONS.ROBOT_VIEW],
    },
    {
      key: ROUTES.FAULT_REPORTS,
      label: '故障报告',
      icon: <ExclamationCircleOutlined />,
      path: ROUTES.FAULT_REPORTS,
      permissions: [PERMISSIONS.FAULT_VIEW],
    },
    {
      key: 'system',
      label: '系统管理',
      icon: <SettingOutlined />,
      permissions: [PERMISSIONS.USER_VIEW, PERMISSIONS.ROLE_VIEW, PERMISSIONS.SETTINGS_VIEW],
      children: [
        {
          key: ROUTES.USER_MANAGEMENT,
          label: '用户管理',
          icon: <UserOutlined />,
          path: ROUTES.USER_MANAGEMENT,
          permissions: [PERMISSIONS.USER_VIEW],
        },
        {
          key: ROUTES.ROLE_MANAGEMENT,
          label: '角色管理',
          icon: <TeamOutlined />,
          path: ROUTES.ROLE_MANAGEMENT,
          permissions: [PERMISSIONS.ROLE_VIEW],
        },
        {
          key: ROUTES.SETTINGS,
          label: '系统设置',
          path: ROUTES.SETTINGS,
          permissions: [PERMISSIONS.SETTINGS_VIEW],
        },
      ],
    },
  ];

  // 过滤有权限的菜单项
  const filterMenuByPermission = (items: MenuItem[]): MenuItem[] => {
    return items.filter((item) => {
      // 如果没有权限要求，显示菜单项
      if (!item.permissions || item.permissions.length === 0) {
        return true;
      }

      // 检查是否有权限
      const hasItemPermission = hasPermission(item.permissions);
      if (!hasItemPermission) {
        return false;
      }

      // 如果有子菜单，递归过滤
      if (item.children) {
        const filteredChildren = filterMenuByPermission(item.children);
        item.children = filteredChildren;
        // 如果子菜单全部被过滤掉，则隐藏父菜单
        return filteredChildren.length > 0;
      }

      return true;
    });
  };

  // 转换菜单数据格式 - 使用 React Router v7 的新导航方式
  const convertMenuItems = (items: MenuItem[]): MenuProps['items'] => {
    return items.map((item) => ({
      key: item.key,
      icon: item.icon,
      label: item.label,
      children: item.children ? convertMenuItems(item.children) : undefined,
      onClick: item.path ? () => {
        // 使用 React Router v7 的新导航选项
        navigate(item.path!, {
          replace: false,
          state: { from: location.pathname }
        });
      } : undefined,
    }));
  };

  // 根据当前路径设置选中的菜单项
  useEffect(() => {
    const currentPath = location.pathname;
    setSelectedKeys([currentPath]);

    // 设置展开的菜单项
    if (currentPath.startsWith('/books')) {
      setOpenKeys(['books']);
    } else if (
      currentPath === ROUTES.USER_MANAGEMENT ||
      currentPath === ROUTES.ROLE_MANAGEMENT ||
      currentPath === ROUTES.SETTINGS
    ) {
      setOpenKeys(['system']);
    }
  }, [location.pathname]);

  // 处理子菜单展开/收起
  const handleOpenChange = (keys: string[]) => {
    setOpenKeys(keys);
  };

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={256}
      className="layout-sidebar"
    >
      <div className="sidebar-logo">
        <div className="logo-icon">📚</div>
        {!collapsed && <div className="logo-text">图书管理</div>}
      </div>
      
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={selectedKeys}
        openKeys={openKeys}
        onOpenChange={handleOpenChange}
        items={convertMenuItems(filterMenuByPermission(menuItems))}
        style={{ borderRight: 0 }}
      />
    </Sider>
  );
};

export default Sidebar;
