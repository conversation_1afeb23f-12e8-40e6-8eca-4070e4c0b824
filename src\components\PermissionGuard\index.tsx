import React from 'react';
import { Result, Button } from 'antd';
import { useNavigate } from 'react-router';
import { usePermission } from '../../hooks/usePermission';
import { ROUTES } from '../../constants';

interface PermissionGuardProps {
  children: React.ReactNode;
  permission?: string | string[];
  role?: string | string[];
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * 权限守卫组件
 * 用于控制组件的显示权限
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  role,
  fallback,
  showFallback = true,
}) => {
  const navigate = useNavigate();
  const { hasPermission, hasRole } = usePermission();

  // 检查权限
  const hasRequiredPermission = permission ? hasPermission(permission) : true;
  const hasRequiredRole = role ? hasRole(role) : true;

  // 如果没有权限
  if (!hasRequiredPermission || !hasRequiredRole) {
    // 如果提供了自定义fallback，使用它
    if (fallback) {
      return <>{fallback}</>;
    }

    // 如果不显示fallback，返回null
    if (!showFallback) {
      return null;
    }

    // 默认的无权限提示
    return (
      <Result
        status="403"
        title="403"
        subTitle="抱歉，您没有权限访问此页面。"
        extra={
          <Button
            type="primary"
            onClick={() => navigate(ROUTES.DASHBOARD, {
              replace: true,
              state: { from: window.location.pathname }
            })}
          >
            返回首页
          </Button>
        }
      />
    );
  }

  return <>{children}</>;
};

export default PermissionGuard;
