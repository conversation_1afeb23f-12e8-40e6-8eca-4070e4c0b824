# 图书馆管理系统

基于 React 19 + TypeScript + Vite + Ant Design 构建的现代化图书馆后台管理系统。

## 🚀 项目特性

- ⚡️ **现代化技术栈**: React 19 + TypeScript + Vite
- 🎨 **优雅UI**: Ant Design 5.x 组件库
- 🔐 **完整权限系统**: 基于角色的权限控制(RBAC)
- 📱 **响应式设计**: 支持桌面端和移动端
- 🛡️ **TypeScript**: 完整的类型定义
- 🔄 **状态管理**: Zustand 轻量级状态管理
- 📦 **模块化**: 清晰的项目结构和模块划分
- 🎯 **路由管理**: React Router v6 + 懒加载
- 🔧 **开发体验**: 热更新 + ESLint + Prettier

## 📋 功能模块

### 🏠 仪表盘
- 系统概览统计
- 实时数据展示
- 快捷操作入口

### 📚 图书管理
- 图书列表查看
- 图书信息增删改查
- 图书分类管理
- 库存状态监控

### 📖 借还记录
- 借书记录管理
- 还书处理
- 续借功能
- 逾期提醒
- 罚金计算

### 🤖 机器人监控
- 实时状态监控
- 电量监控
- 位置追踪
- 任务统计
- 远程控制

### ⚠️ 故障报告
- 故障上报
- 故障分配
- 处理流程
- 状态跟踪

### 👥 系统管理
- 用户管理
- 角色权限
- 系统设置

## 🛠️ 技术栈

- **前端框架**: React 19
- **开发语言**: TypeScript
- **构建工具**: Vite
- **UI组件库**: Ant Design 5.x
- **状态管理**: Zustand
- **路由管理**: React Router v6
- **HTTP客户端**: Axios
- **日期处理**: Day.js
- **样式方案**: CSS + Ant Design

## 📦 安装使用

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产构建
```bash
npm run preview
```

## 🔑 默认账号

系统提供以下测试账号：

| 用户名 | 密码 | 角色 | 权限 |
|--------|------|------|------|
| admin | 123456 | 超级管理员 | 所有权限 |
| librarian | 123456 | 图书管理员 | 图书管理、借还记录等 |

## 📁 项目结构

```
src/
├── components/          # 通用组件
│   ├── Pagination/     # 分页组件
│   └── PermissionGuard/ # 权限守卫组件
├── constants/          # 常量定义
├── hooks/              # 自定义Hooks
├── layout/             # 布局组件
│   ├── Header/         # 顶部导航
│   ├── Sidebar/        # 侧边栏
│   └── Breadcrumb/     # 面包屑
├── pages/              # 页面组件
│   ├── Login/          # 登录页
│   ├── Dashboard/      # 仪表盘
│   ├── Books/          # 图书管理
│   ├── BorrowRecords/  # 借还记录
│   ├── RobotStatus/    # 机器人状态
│   └── FaultReports/   # 故障报告
├── router/             # 路由配置
├── services/           # API服务
├── store/              # 状态管理
├── types/              # 类型定义
└── utils/              # 工具函数
```

## 🔐 权限系统

系统采用基于角色的权限控制(RBAC)：

- **路由权限**: 控制页面访问
- **菜单权限**: 控制菜单显示
- **操作权限**: 控制按钮和功能
- **数据权限**: 控制数据范围

## 📱 响应式设计

- 桌面端: >= 1200px
- 平板端: 768px - 1199px  
- 移动端: < 768px

## 🎯 开发规范

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 组件采用函数式写法
- 使用 Hooks 管理状态
- 统一的错误处理
- 完整的类型定义

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 邮箱: <EMAIL>

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
