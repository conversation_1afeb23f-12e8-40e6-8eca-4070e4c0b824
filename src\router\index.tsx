import { createBrowserRouter, Navigate, redirect } from 'react-router';
import { lazy, Suspense } from 'react';
import { Spin } from 'antd';
import { ROUTES, STORAGE_KEYS } from '../constants';
import AuthGuard from './AuthGuard';
import Layout from '../layout';

// 懒加载组件
const Login = lazy(() => import('../pages/Login'));
const Dashboard = lazy(() => import('../pages/Dashboard'));
const BookList = lazy(() => import('../pages/Books/BookList'));
const BookAdd = lazy(() => import('../pages/Books/BookAdd'));
const BookEdit = lazy(() => import('../pages/Books/BookEdit'));
const BorrowRecords = lazy(() => import('../pages/BorrowRecords'));
const RobotStatus = lazy(() => import('../pages/RobotStatus'));
const FaultReports = lazy(() => import('../pages/FaultReports'));
const UserManagement = lazy(() => import('../pages/UserManagement'));
const RoleManagement = lazy(() => import('../pages/RoleManagement'));
const Settings = lazy(() => import('../pages/Settings'));
const Profile = lazy(() => import('../pages/Profile'));

// 加载中组件
const PageLoading = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '200px' 
  }}>
    <Spin size="large" />
  </div>
);

// 懒加载包装器
const LazyWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<PageLoading />}>
    {children}
  </Suspense>
);

// React Router v7 新特性：路由保护函数
const requireAuth = () => {
  const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
  const userInfo = localStorage.getItem(STORAGE_KEYS.USER_INFO);

  if (!token || !userInfo) {
    throw redirect(ROUTES.LOGIN);
  }

  try {
    const user = JSON.parse(userInfo);
    return { token, user };
  } catch {
    localStorage.removeItem(STORAGE_KEYS.TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER_INFO);
    throw redirect(ROUTES.LOGIN);
  }
};

// 检查是否已登录，如果已登录则重定向到仪表盘
const redirectIfAuthenticated = () => {
  const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
  if (token) {
    throw redirect(ROUTES.DASHBOARD);
  }
  return null;
};

// 路由配置 - 使用 React Router v7 的新特性
export const router = createBrowserRouter([
  {
    path: ROUTES.LOGIN,
    element: (
      <LazyWrapper>
        <Login />
      </LazyWrapper>
    ),
    loader: redirectIfAuthenticated, // 如果已登录则重定向
  },
  {
    path: '/',
    element: (
      <AuthGuard>
        <Layout />
      </AuthGuard>
    ),
    loader: requireAuth, // 需要认证才能访问
    children: [
      {
        index: true,
        element: <Navigate to={ROUTES.DASHBOARD} replace />,
      },
      {
        path: ROUTES.DASHBOARD,
        element: (
          <LazyWrapper>
            <Dashboard />
          </LazyWrapper>
        ),
      },
      {
        path: ROUTES.BOOK_LIST,
        element: (
          <LazyWrapper>
            <BookList />
          </LazyWrapper>
        ),
      },
      {
        path: ROUTES.BOOK_ADD,
        element: (
          <LazyWrapper>
            <BookAdd />
          </LazyWrapper>
        ),
      },
      {
        path: `${ROUTES.BOOK_EDIT}/:id`,
        element: (
          <LazyWrapper>
            <BookEdit />
          </LazyWrapper>
        ),
      },
      {
        path: ROUTES.BORROW_RECORDS,
        element: (
          <LazyWrapper>
            <BorrowRecords />
          </LazyWrapper>
        ),
      },
      {
        path: ROUTES.ROBOT_STATUS,
        element: (
          <LazyWrapper>
            <RobotStatus />
          </LazyWrapper>
        ),
      },
      {
        path: ROUTES.FAULT_REPORTS,
        element: (
          <LazyWrapper>
            <FaultReports />
          </LazyWrapper>
        ),
      },
      {
        path: ROUTES.USER_MANAGEMENT,
        element: (
          <LazyWrapper>
            <UserManagement />
          </LazyWrapper>
        ),
      },
      {
        path: ROUTES.ROLE_MANAGEMENT,
        element: (
          <LazyWrapper>
            <RoleManagement />
          </LazyWrapper>
        ),
      },
      {
        path: ROUTES.SETTINGS,
        element: (
          <LazyWrapper>
            <Settings />
          </LazyWrapper>
        ),
      },
      {
        path: ROUTES.PROFILE,
        element: (
          <LazyWrapper>
            <Profile />
          </LazyWrapper>
        ),
      },
    ],
  },
  {
    path: '*',
    element: <Navigate to={ROUTES.DASHBOARD} replace />,
  },
]);

export default router;
