import { useEffect } from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router';
import { useAuthStore } from '../store/authStore';
import { ROUTES, STORAGE_KEYS } from '../constants';

interface AuthGuardProps {
  children: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const location = useLocation();
  const { isAuthenticated, user, setUser, setToken } = useAuthStore();

  useEffect(() => {
    // 检查本地存储中的token和用户信息
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
    const userInfo = localStorage.getItem(STORAGE_KEYS.USER_INFO);

    if (token && userInfo) {
      try {
        const parsedUser = JSON.parse(userInfo);
        setToken(token);
        setUser(parsedUser);
      } catch (error) {
        console.error('Failed to parse user info from localStorage:', error);
        // 清除无效的本地存储数据
        localStorage.removeItem(STORAGE_KEYS.TOKEN);
        localStorage.removeItem(STORAGE_KEYS.USER_INFO);
      }
    }
  }, [setUser, setToken]);

  // 如果用户未认证，重定向到登录页
  if (!isAuthenticated) {
    return (
      <Navigate 
        to={ROUTES.LOGIN} 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // 检查用户权限（这里可以根据路由配置检查具体权限）
  // TODO: 实现基于路由的权限检查
  const hasPermission = checkRoutePermission(location.pathname, user);
  
  if (!hasPermission) {
    // 可以重定向到无权限页面或显示403错误
    return (
      <Navigate 
        to={ROUTES.DASHBOARD} 
        replace 
      />
    );
  }

  return <>{children}</>;
};

// 检查路由权限的辅助函数
function checkRoutePermission(pathname: string, user: any): boolean {
  // 这里可以实现具体的权限检查逻辑
  // 暂时返回true，后续在权限管理模块中完善
  
  if (!user) {
    return false;
  }

  // 超级管理员拥有所有权限
  if (user.role?.name === 'super_admin') {
    return true;
  }

  // 根据路径和用户权限进行检查
  // TODO: 实现具体的权限检查逻辑
  
  return true;
}

export default AuthGuard;
