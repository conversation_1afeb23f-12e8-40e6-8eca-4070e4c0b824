import type { BorrowRecord, ApiResponse, PaginatedResponse, PaginationParams } from '../types/index';
import dayjs from 'dayjs';

// 模拟借还书记录数据
const mockBorrowRecords: BorrowRecord[] = [
  {
    id: '1',
    bookId: '1',
    bookTitle: 'JavaScript高级程序设计',
    userId: 'user1',
    userName: '张三',
    borrowDate: '2024-01-15T09:00:00Z',
    dueDate: '2024-02-15T23:59:59Z',
    returnDate: '2024-02-10T14:30:00Z',
    status: 'returned',
    renewCount: 0,
    fine: 0,
  },
  {
    id: '2',
    bookId: '2',
    bookTitle: 'React实战',
    userId: 'user2',
    userName: '李四',
    borrowDate: '2024-01-20T10:15:00Z',
    dueDate: '2024-02-20T23:59:59Z',
    status: 'borrowed',
    renewCount: 1,
  },
  {
    id: '3',
    bookId: '3',
    bookTitle: 'TypeScript入门与实战',
    userId: 'user3',
    userName: '王五',
    borrowDate: '2024-01-10T08:30:00Z',
    dueDate: '2024-02-10T23:59:59Z',
    returnDate: '2024-02-18T16:45:00Z',
    status: 'returned',
    renewCount: 0,
    fine: 8, // 逾期8天，每天1元
  },
  {
    id: '4',
    bookId: '4',
    bookTitle: 'Vue.js实战',
    userId: 'user4',
    userName: '赵六',
    borrowDate: '2024-01-05T11:20:00Z',
    dueDate: '2024-02-05T23:59:59Z',
    status: 'overdue',
    renewCount: 0,
  },
  {
    id: '5',
    bookId: '5',
    bookTitle: 'Node.js开发指南',
    userId: 'user5',
    userName: '钱七',
    borrowDate: '2024-01-25T13:45:00Z',
    dueDate: '2024-02-25T23:59:59Z',
    status: 'borrowed',
    renewCount: 0,
  },
  {
    id: '6',
    bookId: '1',
    bookTitle: 'JavaScript高级程序设计',
    userId: 'user6',
    userName: '孙八',
    borrowDate: '2024-02-01T09:15:00Z',
    dueDate: '2024-03-01T23:59:59Z',
    status: 'borrowed',
    renewCount: 0,
  },
];

// 获取借还书记录列表
export const getBorrowRecords = async (
  params: PaginationParams & {
    bookTitle?: string;
    userName?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
  }
): Promise<ApiResponse<PaginatedResponse<BorrowRecord>>> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 800));

  let filteredRecords = [...mockBorrowRecords];

  // 搜索过滤
  if (params.bookTitle) {
    filteredRecords = filteredRecords.filter(record =>
      record.bookTitle.toLowerCase().includes(params.bookTitle!.toLowerCase())
    );
  }
  if (params.userName) {
    filteredRecords = filteredRecords.filter(record =>
      record.userName.toLowerCase().includes(params.userName!.toLowerCase())
    );
  }
  if (params.status) {
    filteredRecords = filteredRecords.filter(record =>
      record.status === params.status
    );
  }
  if (params.startDate) {
    filteredRecords = filteredRecords.filter(record =>
      dayjs(record.borrowDate).isAfter(dayjs(params.startDate).subtract(1, 'day'))
    );
  }
  if (params.endDate) {
    filteredRecords = filteredRecords.filter(record =>
      dayjs(record.borrowDate).isBefore(dayjs(params.endDate).add(1, 'day'))
    );
  }

  // 按借书时间倒序排列
  filteredRecords.sort((a, b) => 
    dayjs(b.borrowDate).valueOf() - dayjs(a.borrowDate).valueOf()
  );

  // 分页
  const total = filteredRecords.length;
  const start = (params.page - 1) * params.pageSize;
  const end = start + params.pageSize;
  const list = filteredRecords.slice(start, end);

  return {
    code: 200,
    message: '获取成功',
    data: {
      list,
      pagination: {
        current: params.page,
        pageSize: params.pageSize,
        total,
      },
    },
    success: true,
  };
};

// 获取借还书统计数据
export const getBorrowStatistics = async (): Promise<ApiResponse<{
  totalBorrows: number;
  currentBorrows: number;
  overdueBorrows: number;
  totalReturns: number;
  totalFines: number;
}>> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  const totalBorrows = mockBorrowRecords.length;
  const currentBorrows = mockBorrowRecords.filter(r => r.status === 'borrowed').length;
  const overdueBorrows = mockBorrowRecords.filter(r => r.status === 'overdue').length;
  const totalReturns = mockBorrowRecords.filter(r => r.status === 'returned').length;
  const totalFines = mockBorrowRecords.reduce((sum, r) => sum + (r.fine || 0), 0);

  return {
    code: 200,
    message: '获取成功',
    data: {
      totalBorrows,
      currentBorrows,
      overdueBorrows,
      totalReturns,
      totalFines,
    },
    success: true,
  };
};

// 处理还书
export const returnBook = async (recordId: string): Promise<ApiResponse<BorrowRecord>> => {
  await new Promise(resolve => setTimeout(resolve, 1000));

  const recordIndex = mockBorrowRecords.findIndex(r => r.id === recordId);
  if (recordIndex === -1) {
    return {
      code: 404,
      message: '借书记录不存在',
      data: null,
      success: false,
    };
  }

  const record = mockBorrowRecords[recordIndex];
  if (record.status === 'returned') {
    return {
      code: 400,
      message: '该图书已经归还',
      data: null,
      success: false,
    };
  }

  // 计算是否逾期和罚金
  const now = dayjs();
  const dueDate = dayjs(record.dueDate);
  const isOverdue = now.isAfter(dueDate);
  const overdueDays = isOverdue ? now.diff(dueDate, 'day') : 0;
  const fine = overdueDays * 1; // 每天1元罚金

  const updatedRecord: BorrowRecord = {
    ...record,
    returnDate: now.toISOString(),
    status: 'returned',
    fine: fine,
  };

  mockBorrowRecords[recordIndex] = updatedRecord;

  return {
    code: 200,
    message: '还书成功',
    data: updatedRecord,
    success: true,
  };
};

// 续借图书
export const renewBook = async (recordId: string): Promise<ApiResponse<BorrowRecord>> => {
  await new Promise(resolve => setTimeout(resolve, 800));

  const recordIndex = mockBorrowRecords.findIndex(r => r.id === recordId);
  if (recordIndex === -1) {
    return {
      code: 404,
      message: '借书记录不存在',
      data: null,
      success: false,
    };
  }

  const record = mockBorrowRecords[recordIndex];
  if (record.status !== 'borrowed') {
    return {
      code: 400,
      message: '只有正在借阅的图书才能续借',
      data: null,
      success: false,
    };
  }

  if (record.renewCount >= 2) {
    return {
      code: 400,
      message: '该图书已达到最大续借次数',
      data: null,
      success: false,
    };
  }

  // 续借30天
  const newDueDate = dayjs(record.dueDate).add(30, 'day');
  const updatedRecord: BorrowRecord = {
    ...record,
    dueDate: newDueDate.toISOString(),
    renewCount: record.renewCount + 1,
  };

  mockBorrowRecords[recordIndex] = updatedRecord;

  return {
    code: 200,
    message: '续借成功',
    data: updatedRecord,
    success: true,
  };
};

// 获取逾期记录
export const getOverdueRecords = async (): Promise<ApiResponse<BorrowRecord[]>> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  const overdueRecords = mockBorrowRecords.filter(record => {
    if (record.status === 'returned') return false;
    return dayjs().isAfter(dayjs(record.dueDate));
  });

  return {
    code: 200,
    message: '获取成功',
    data: overdueRecords,
    success: true,
  };
};
