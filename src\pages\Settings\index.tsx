import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Switch,
  Select,
  Button,
  Space,
  Typography,
  Divider,
  Row,
  Col,
  InputNumber,
  TimePicker,
  message,
  Tabs,
} from 'antd';
import {
  SettingOutlined,
  SecurityScanOutlined,
  NotificationOutlined,
  DatabaseOutlined,
  CloudOutlined,
  UserOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import './index.css';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface SystemSettings {
  // 基本设置
  systemName: string;
  systemDescription: string;
  adminEmail: string;
  timezone: string;
  language: string;

  // 安全设置
  passwordMinLength: number;
  sessionTimeout: number;
  maxLoginAttempts: number;
  enableTwoFactor: boolean;

  // 通知设置
  emailNotifications: boolean;
  smsNotifications: boolean;
  systemAlerts: boolean;
  maintenanceNotice: boolean;

  // 数据库设置
  backupEnabled: boolean;
  backupTime: string;
  backupRetentionDays: number;

  // 系统维护
  maintenanceMode: boolean;
  maintenanceMessage: string;
}

const Settings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState<SystemSettings>({
    systemName: '图书管理系统',
    systemDescription: '智能图书借还管理平台',
    adminEmail: '<EMAIL>',
    timezone: 'Asia/Shanghai',
    language: 'zh-CN',
    passwordMinLength: 6,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    enableTwoFactor: false,
    emailNotifications: true,
    smsNotifications: false,
    systemAlerts: true,
    maintenanceNotice: true,
    backupEnabled: true,
    backupTime: '02:00',
    backupRetentionDays: 30,
    maintenanceMode: false,
    maintenanceMessage: '系统正在维护中，请稍后再试...',
  });

  const handleSave = async (values: SystemSettings) => {
    setLoading(true);
    try {
      // 模拟保存设置
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSettings(values);
      message.success('设置保存成功！');
    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.resetFields();
    message.info('已重置为默认设置');
  };

  const basicSettingsForm = (
    <Card title={<><SettingOutlined /> 基本设置</>} className="settings-card">
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            label="系统名称"
            name="systemName"
            rules={[{ required: true, message: '请输入系统名称' }]}
          >
            <Input placeholder="请输入系统名称" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="管理员邮箱"
            name="adminEmail"
            rules={[
              { required: true, message: '请输入管理员邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input placeholder="请输入管理员邮箱" />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        label="系统描述"
        name="systemDescription"
      >
        <TextArea rows={3} placeholder="请输入系统描述" />
      </Form.Item>

      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            label="时区"
            name="timezone"
          >
            <Select placeholder="请选择时区">
              <Option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</Option>
              <Option value="Asia/Tokyo">Asia/Tokyo (UTC+9)</Option>
              <Option value="America/New_York">America/New_York (UTC-5)</Option>
              <Option value="Europe/London">Europe/London (UTC+0)</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="系统语言"
            name="language"
          >
            <Select placeholder="请选择语言">
              <Option value="zh-CN">简体中文</Option>
              <Option value="zh-TW">繁体中文</Option>
              <Option value="en-US">English</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );

  const securitySettingsForm = (
    <Card title={<><SecurityScanOutlined /> 安全设置</>} className="settings-card">
      <Row gutter={24}>
        <Col span={8}>
          <Form.Item
            label="密码最小长度"
            name="passwordMinLength"
            rules={[{ required: true, message: '请输入密码最小长度' }]}
          >
            <InputNumber min={6} max={20} placeholder="6" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="会话超时(分钟)"
            name="sessionTimeout"
            rules={[{ required: true, message: '请输入会话超时时间' }]}
          >
            <InputNumber min={5} max={120} placeholder="30" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="最大登录尝试次数"
            name="maxLoginAttempts"
            rules={[{ required: true, message: '请输入最大登录尝试次数' }]}
          >
            <InputNumber min={3} max={10} placeholder="5" />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        label="启用双因子认证"
        name="enableTwoFactor"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>
    </Card>
  );

  const notificationSettingsForm = (
    <Card title={<><NotificationOutlined /> 通知设置</>} className="settings-card">
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            label="邮件通知"
            name="emailNotifications"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label="系统警报"
            name="systemAlerts"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="短信通知"
            name="smsNotifications"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label="维护通知"
            name="maintenanceNotice"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );

  const databaseSettingsForm = (
    <Card title={<><DatabaseOutlined /> 数据库设置</>} className="settings-card">
      <Form.Item
        label="启用自动备份"
        name="backupEnabled"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            label="备份时间"
            name="backupTime"
          >
            <TimePicker
              format="HH:mm"
              placeholder="选择备份时间"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="备份保留天数"
            name="backupRetentionDays"
            rules={[{ required: true, message: '请输入备份保留天数' }]}
          >
            <InputNumber min={7} max={365} placeholder="30" />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );

  const maintenanceSettingsForm = (
    <Card title={<><CloudOutlined /> 系统维护</>} className="settings-card">
      <Form.Item
        label="维护模式"
        name="maintenanceMode"
        valuePropName="checked"
        extra="开启后，普通用户将无法访问系统"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        label="维护提示信息"
        name="maintenanceMessage"
      >
        <TextArea rows={3} placeholder="请输入维护提示信息" />
      </Form.Item>
    </Card>
  );

  const tabItems = [
    {
      key: 'basic',
      label: <><SettingOutlined /> 基本设置</>,
      children: basicSettingsForm,
    },
    {
      key: 'security',
      label: <><SecurityScanOutlined /> 安全设置</>,
      children: securitySettingsForm,
    },
    {
      key: 'notification',
      label: <><NotificationOutlined /> 通知设置</>,
      children: notificationSettingsForm,
    },
    {
      key: 'database',
      label: <><DatabaseOutlined /> 数据库设置</>,
      children: databaseSettingsForm,
    },
    {
      key: 'maintenance',
      label: <><CloudOutlined /> 系统维护</>,
      children: maintenanceSettingsForm,
    },
  ];

  return (
    <div className="settings-container">
      <div className="settings-header">
        <Title level={2}>
          <SettingOutlined /> 系统设置
        </Title>
        <Text type="secondary">管理系统的各项配置参数</Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={settings}
        onFinish={handleSave}
        className="settings-form"
      >
        <Tabs items={tabItems} />

        <div className="settings-actions">
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              保存设置
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </div>
      </Form>
    </div>
  );
};

export default Settings;
