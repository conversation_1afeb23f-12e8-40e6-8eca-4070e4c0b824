import React from 'react';
import { Pagination as AntPagination } from 'antd';
import type { PaginationProps as AntPaginationProps } from 'antd';
import { PAGINATION_CONFIG } from '../../constants';
import './index.css';

interface PaginationProps extends Omit<AntPaginationProps, 'onChange' | 'onShowSizeChange'> {
  current: number;
  total: number;
  pageSize: number;
  onChange: (page: number, pageSize: number) => void;
  showTotal?: boolean;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  pageSizeOptions?: string[];
}

const Pagination: React.FC<PaginationProps> = ({
  current,
  total,
  pageSize,
  onChange,
  showTotal = true,
  showSizeChanger = PAGINATION_CONFIG.SHOW_SIZE_CHANGER,
  showQuickJumper = PAGINATION_CONFIG.SHOW_QUICK_JUMPER,
  pageSizeOptions = PAGINATION_CONFIG.PAGE_SIZE_OPTIONS,
  ...restProps
}) => {
  // 处理页码变化
  const handleChange = (page: number, size: number): void => {
    onChange(page, size);
  };

  // 处理每页条数变化
  const handleShowSizeChange = (current: number, size: number): void => {
    onChange(1, size); // 改变每页条数时回到第一页
  };

  // 自定义显示总数的函数
  const showTotalFunc = (total: number, range: [number, number]): string => {
    return `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`;
  };

  return (
    <div className="custom-pagination">
      <AntPagination
        current={current}
        total={total}
        pageSize={pageSize}
        showSizeChanger={showSizeChanger}
        showQuickJumper={showQuickJumper}
        pageSizeOptions={pageSizeOptions}
        showTotal={showTotal ? showTotalFunc : undefined}
        onChange={handleChange}
        onShowSizeChange={handleShowSizeChange}
        {...restProps}
      />
    </div>
  );
};

export default Pagination;
