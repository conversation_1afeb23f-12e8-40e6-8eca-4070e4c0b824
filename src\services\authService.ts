import type { User, ApiResponse } from '../types/index';
import { USER_ROLES, PERMISSIONS } from '../constants';

// 模拟用户数据
const mockUsers = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    password: '123456',
    role: {
      id: '1',
      name: USER_ROLES.SUPER_ADMIN,
      permissions: Object.values(PERMISSIONS).map(code => ({
        id: code,
        name: code,
        code,
        type: 'action' as const,
      })),
    },
    avatar: '',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    username: 'librarian',
    email: '<EMAIL>',
    password: '123456',
    role: {
      id: '2',
      name: USER_ROLES.LIBRARIAN,
      permissions: [
        PERMISSIONS.BOOK_VIEW,
        PERMISSIONS.BOOK_ADD,
        PERMISSIONS.BOOK_EDIT,
        PERMISSIONS.BORROW_VIEW,
        PERMISSIONS.BORROW_MANAGE,
        PERMISSIONS.ROBOT_VIEW,
        PERMISSIONS.FAULT_VIEW,
      ].map(code => ({
        id: code,
        name: code,
        code,
        type: 'action' as const,
      })),
    },
    avatar: '',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
];

// 模拟登录API
export const mockLogin = async (
  username: string,
  password: string
): Promise<ApiResponse<{ token: string; user: User }>> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  const user = mockUsers.find(
    u => u.username === username && u.password === password
  );

  if (!user) {
    return {
      code: 401,
      message: '用户名或密码错误',
      data: null,
      success: false,
    };
  }

  // 生成模拟token
  const token = `mock_token_${user.id}_${Date.now()}`;

  // 移除密码字段
  const { password: _, ...userWithoutPassword } = user;

  return {
    code: 200,
    message: '登录成功',
    data: {
      token,
      user: userWithoutPassword as User,
    },
    success: true,
  };
};

// 模拟验证token
export const mockVerifyToken = async (
  token: string
): Promise<ApiResponse<User>> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  // 简单的token验证逻辑
  if (!token || !token.startsWith('mock_token_')) {
    return {
      code: 401,
      message: 'Token无效',
      data: null,
      success: false,
    };
  }

  // 从token中提取用户ID
  const userId = token.split('_')[2];
  const user = mockUsers.find(u => u.id === userId);

  if (!user) {
    return {
      code: 401,
      message: '用户不存在',
      data: null,
      success: false,
    };
  }

  // 移除密码字段
  const { password: _, ...userWithoutPassword } = user;

  return {
    code: 200,
    message: '验证成功',
    data: userWithoutPassword as User,
    success: true,
  };
};

// 模拟登出API
export const mockLogout = async (): Promise<ApiResponse<null>> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  return {
    code: 200,
    message: '登出成功',
    data: null,
    success: true,
  };
};

// 模拟修改密码API
export const mockChangePassword = async (
  oldPassword: string,
  newPassword: string
): Promise<ApiResponse<null>> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 800));

  // 这里可以添加密码验证逻辑
  if (oldPassword === newPassword) {
    return {
      code: 400,
      message: '新密码不能与旧密码相同',
      data: null,
      success: false,
    };
  }

  return {
    code: 200,
    message: '密码修改成功',
    data: null,
    success: true,
  };
};
