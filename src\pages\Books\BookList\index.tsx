import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  Space,
  Typography,
  Tag,
  Modal,
  message,
  Tooltip,
} from 'antd';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router';
import type { Book } from '../../../types/index';
import { getBookList, deleteBook, getBookCategories } from '../../../services/bookService';
import { ROUTES, PAGINATION_CONFIG } from '../../../constants';
import { usePermission } from '../../../hooks/usePermission';
import PermissionGuard from '../../../components/PermissionGuard';
import Pagination from '../../../components/Pagination';
import { PERMISSIONS } from '../../../constants';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

interface SearchParams {
  title?: string;
  author?: string;
  category?: string;
}

const BookList: React.FC = () => {
  const navigate = useNavigate();
  const { hasPermission } = usePermission();

  const [books, setBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [categories, setCategories] = useState<string[]>([]);
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: PAGINATION_CONFIG.DEFAULT_PAGE_SIZE,
    total: 0,
  });

  // 获取图书列表
  const fetchBooks = async (page = 1, pageSize = pagination.pageSize, search = searchParams) => {
    setLoading(true);
    try {
      const response = await getBookList({
        page,
        pageSize,
        ...search,
      });

      if (response.success) {
        setBooks(response.data.list);
        setPagination({
          current: response.data.pagination.current,
          pageSize: response.data.pagination.pageSize,
          total: response.data.pagination.total,
        });
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('获取图书列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await getBookCategories();
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('获取分类失败:', error);
    }
  };

  useEffect(() => {
    fetchBooks();
    fetchCategories();
  }, []);

  // 处理搜索
  const handleSearch = (values: SearchParams) => {
    setSearchParams(values);
    fetchBooks(1, pagination.pageSize, values);
  };

  // 处理分页变化
  const handlePaginationChange = (page: number, pageSize: number) => {
    fetchBooks(page, pageSize);
  };

  // 处理删除
  const handleDelete = (book: Book) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除图书《${book.title}》吗？`,
      onOk: async () => {
        try {
          const response = await deleteBook(book.id);
          if (response.success) {
            message.success('删除成功');
            fetchBooks(pagination.current, pagination.pageSize);
          } else {
            message.error(response.message);
          }
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  // 表格列配置
  const columns: ColumnsType<Book> = [
    {
      title: '书名',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (title) => (
        <Tooltip placement="topLeft" title={title}>
          {title}
        </Tooltip>
      ),
    },
    {
      title: '作者',
      dataIndex: 'author',
      key: 'author',
      width: 150,
    },
    {
      title: 'ISBN',
      dataIndex: 'isbn',
      key: 'isbn',
      width: 130,
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category) => <Tag color="blue">{category}</Tag>,
    },
    {
      title: '出版社',
      dataIndex: 'publisher',
      key: 'publisher',
      width: 150,
      ellipsis: true,
    },
    {
      title: '库存',
      key: 'copies',
      width: 100,
      render: (_, record) => (
        <span>
          <span style={{ color: record.availableCopies > 0 ? '#52c41a' : '#f5222d' }}>
            {record.availableCopies}
          </span>
          /{record.totalCopies}
        </span>
      ),
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <PermissionGuard permission={PERMISSIONS.BOOK_EDIT} showFallback={false}>
            <Tooltip title="编辑">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => navigate(`${ROUTES.BOOK_EDIT}/${record.id}`, {
                  state: {
                    from: window.location.pathname,
                    bookData: record
                  }
                })}
              />
            </Tooltip>
          </PermissionGuard>
          <PermissionGuard permission={PERMISSIONS.BOOK_DELETE} showFallback={false}>
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(record)}
              />
            </Tooltip>
          </PermissionGuard>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2} style={{ margin: 0 }}>图书管理</Title>
        <PermissionGuard permission={PERMISSIONS.BOOK_ADD} showFallback={false}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate(ROUTES.BOOK_ADD, {
              state: { from: window.location.pathname }
            })}
          >
            添加图书
          </Button>
        </PermissionGuard>
      </div>

      <Card>
        {/* 搜索区域 */}
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Search
              placeholder="搜索书名"
              allowClear
              style={{ width: 200 }}
              onSearch={(value) => handleSearch({ ...searchParams, title: value })}
            />
            <Search
              placeholder="搜索作者"
              allowClear
              style={{ width: 200 }}
              onSearch={(value) => handleSearch({ ...searchParams, author: value })}
            />
            <Select
              placeholder="选择分类"
              allowClear
              style={{ width: 150 }}
              onChange={(value) => handleSearch({ ...searchParams, category: value })}
            >
              {categories.map(category => (
                <Option key={category} value={category}>
                  {category}
                </Option>
              ))}
            </Select>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                setSearchParams({});
                fetchBooks(1, pagination.pageSize, {});
              }}
            >
              重置
            </Button>
          </Space>
        </div>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={books}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
        />

        {/* 分页 */}
        <Pagination
          current={pagination.current}
          total={pagination.total}
          pageSize={pagination.pageSize}
          onChange={handlePaginationChange}
        />
      </Card>
    </div>
  );
};

export default BookList;
