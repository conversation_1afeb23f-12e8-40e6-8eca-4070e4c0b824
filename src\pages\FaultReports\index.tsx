import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  DatePicker,
  Space,
  Typography,
  Tag,
  Modal,
  message,
  Tooltip,
  Row,
  Col,
  Statistic,
  Form,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  SearchOutlined,
  ReloadOutlined,
  UserAddOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import type { FaultReport } from '../../types/index';
import {
  getFaultReports,
  getFaultStatistics,
  assignFaultReport,
  resolveFaultReport,
  closeFaultReport
} from '../../services/faultService';
import { PAGINATION_CONFIG, FAULT_TYPES, FAULT_SEVERITY } from '../../constants';
import { usePermission } from '../../hooks/usePermission';
import PermissionGuard from '../../components/PermissionGuard';
import Pagination from '../../components/Pagination';
import { PERMISSIONS } from '../../constants';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

interface SearchParams {
  robotName?: string;
  faultType?: string;
  severity?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
}

interface Statistics {
  total: number;
  pending: number;
  processing: number;
  resolved: number;
  closed: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
}

const FaultReports: React.FC = () => {
  const { hasPermission } = usePermission();

  const [reports, setReports] = useState<FaultReport[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [statistics, setStatistics] = useState<Statistics>({
    total: 0,
    pending: 0,
    processing: 0,
    resolved: 0,
    closed: 0,
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
  });
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: PAGINATION_CONFIG.DEFAULT_PAGE_SIZE,
    total: 0,
  });

  // 模态框状态
  const [assignModalVisible, setAssignModalVisible] = useState<boolean>(false);
  const [resolveModalVisible, setResolveModalVisible] = useState<boolean>(false);
  const [currentReport, setCurrentReport] = useState<FaultReport | null>(null);
  const [assignForm] = Form.useForm();
  const [resolveForm] = Form.useForm();

  // 获取故障报告列表
  const fetchReports = async (page = 1, pageSize = pagination.pageSize, search = searchParams) => {
    setLoading(true);
    try {
      const response = await getFaultReports({
        page,
        pageSize,
        ...search,
      });

      if (response.success) {
        setReports(response.data.list);
        setPagination({
          current: response.data.pagination.current,
          pageSize: response.data.pagination.pageSize,
          total: response.data.pagination.total,
        });
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('获取故障报告失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      const response = await getFaultStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  useEffect(() => {
    fetchReports();
    fetchStatistics();
  }, []);

  // 处理搜索
  const handleSearch = (values: SearchParams) => {
    setSearchParams(values);
    fetchReports(1, pagination.pageSize, values);
  };

  // 处理分页变化
  const handlePaginationChange = (page: number, pageSize: number) => {
    fetchReports(page, pageSize);
  };

  // 处理分配
  const handleAssign = (report: FaultReport) => {
    setCurrentReport(report);
    setAssignModalVisible(true);
    assignForm.resetFields();
  };

  // 提交分配
  const handleAssignSubmit = async (values: { assignedTo: string }) => {
    if (!currentReport) return;

    try {
      const response = await assignFaultReport(currentReport.id, values.assignedTo);
      if (response.success) {
        message.success('分配成功');
        setAssignModalVisible(false);
        fetchReports(pagination.current, pagination.pageSize);
        fetchStatistics();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('分配失败');
    }
  };

  // 处理解决
  const handleResolve = (report: FaultReport) => {
    setCurrentReport(report);
    setResolveModalVisible(true);
    resolveForm.resetFields();
  };

  // 提交解决方案
  const handleResolveSubmit = async (values: { solution: string }) => {
    if (!currentReport) return;

    try {
      const response = await resolveFaultReport(currentReport.id, values.solution);
      if (response.success) {
        message.success('故障已解决');
        setResolveModalVisible(false);
        fetchReports(pagination.current, pagination.pageSize);
        fetchStatistics();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 处理关闭
  const handleClose = (report: FaultReport) => {
    Modal.confirm({
      title: '确认关闭',
      content: `确定要关闭故障报告"${report.robotName} - ${report.description}"吗？`,
      onOk: async () => {
        try {
          const response = await closeFaultReport(report.id);
          if (response.success) {
            message.success('故障报告已关闭');
            fetchReports(pagination.current, pagination.pageSize);
            fetchStatistics();
          } else {
            message.error(response.message);
          }
        } catch (error) {
          message.error('关闭失败');
        }
      },
    });
  };

  // 获取严重程度标签
  const getSeverityTag = (severity: string) => {
    const severityConfig = {
      [FAULT_SEVERITY.CRITICAL]: { color: 'red', text: '严重' },
      [FAULT_SEVERITY.HIGH]: { color: 'orange', text: '高' },
      [FAULT_SEVERITY.MEDIUM]: { color: 'yellow', text: '中' },
      [FAULT_SEVERITY.LOW]: { color: 'green', text: '低' },
    };

    const config = severityConfig[severity] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig = {
      pending: { color: 'default', text: '待处理' },
      processing: { color: 'blue', text: '处理中' },
      resolved: { color: 'green', text: '已解决' },
      closed: { color: 'gray', text: '已关闭' },
    };

    const config = statusConfig[status] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取故障类型标签
  const getFaultTypeTag = (type: string) => {
    const typeConfig = {
      [FAULT_TYPES.HARDWARE]: { color: 'red', text: '硬件故障' },
      [FAULT_TYPES.SOFTWARE]: { color: 'blue', text: '软件故障' },
      [FAULT_TYPES.NETWORK]: { color: 'purple', text: '网络故障' },
      [FAULT_TYPES.OTHER]: { color: 'default', text: '其他' },
    };

    const config = typeConfig[type] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列配置
  const columns: ColumnsType<FaultReport> = [
    {
      title: '机器人',
      dataIndex: 'robotName',
      key: 'robotName',
      width: 150,
    },
    {
      title: '故障类型',
      dataIndex: 'faultType',
      key: 'faultType',
      width: 100,
      render: (type) => getFaultTypeTag(type),
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity) => getSeverityTag(severity),
    },
    {
      title: '故障描述',
      dataIndex: 'description',
      key: 'description',
      width: 250,
      ellipsis: {
        showTitle: false,
      },
      render: (description) => (
        <Tooltip placement="topLeft" title={description}>
          {description}
        </Tooltip>
      ),
    },
    {
      title: '报告时间',
      dataIndex: 'reportTime',
      key: 'reportTime',
      width: 120,
      render: (time) => dayjs(time).format('MM-DD HH:mm'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => getStatusTag(status),
    },
    {
      title: '负责人',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      width: 120,
      render: (assignedTo) => assignedTo || '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          {record.status === 'pending' && (
            <PermissionGuard permission={PERMISSIONS.FAULT_HANDLE} showFallback={false}>
              <Tooltip title="分配">
                <Button
                  type="text"
                  icon={<UserAddOutlined />}
                  onClick={() => handleAssign(record)}
                />
              </Tooltip>
            </PermissionGuard>
          )}
          {record.status === 'processing' && (
            <PermissionGuard permission={PERMISSIONS.FAULT_HANDLE} showFallback={false}>
              <Tooltip title="解决">
                <Button
                  type="text"
                  icon={<CheckCircleOutlined />}
                  onClick={() => handleResolve(record)}
                />
              </Tooltip>
            </PermissionGuard>
          )}
          {record.status === 'resolved' && (
            <PermissionGuard permission={PERMISSIONS.FAULT_HANDLE} showFallback={false}>
              <Tooltip title="关闭">
                <Button
                  type="text"
                  icon={<CloseCircleOutlined />}
                  onClick={() => handleClose(record)}
                />
              </Tooltip>
            </PermissionGuard>
          )}
        </Space>
      ),
    },
  ];

  return (
    <PermissionGuard permission={PERMISSIONS.FAULT_VIEW}>
      <div>
        <Title level={2} style={{ marginBottom: 16 }}>故障报告</Title>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="总报告数"
                value={statistics.total}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="待处理"
                value={statistics.pending}
                valueStyle={{ color: '#faad14' }}
                prefix={<ExclamationCircleOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="处理中"
                value={statistics.processing}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="已解决"
                value={statistics.resolved}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="严重故障"
                value={statistics.critical}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        <Card>
          {/* 搜索区域 */}
          <div style={{ marginBottom: 16 }}>
            <Space wrap>
              <Search
                placeholder="搜索机器人名称"
                allowClear
                style={{ width: 200 }}
                onSearch={(value) => handleSearch({ ...searchParams, robotName: value })}
              />
              <Select
                placeholder="故障类型"
                allowClear
                style={{ width: 120 }}
                onChange={(value) => handleSearch({ ...searchParams, faultType: value })}
              >
                <Option value={FAULT_TYPES.HARDWARE}>硬件故障</Option>
                <Option value={FAULT_TYPES.SOFTWARE}>软件故障</Option>
                <Option value={FAULT_TYPES.NETWORK}>网络故障</Option>
                <Option value={FAULT_TYPES.OTHER}>其他</Option>
              </Select>
              <Select
                placeholder="严重程度"
                allowClear
                style={{ width: 120 }}
                onChange={(value) => handleSearch({ ...searchParams, severity: value })}
              >
                <Option value={FAULT_SEVERITY.CRITICAL}>严重</Option>
                <Option value={FAULT_SEVERITY.HIGH}>高</Option>
                <Option value={FAULT_SEVERITY.MEDIUM}>中</Option>
                <Option value={FAULT_SEVERITY.LOW}>低</Option>
              </Select>
              <Select
                placeholder="状态"
                allowClear
                style={{ width: 120 }}
                onChange={(value) => handleSearch({ ...searchParams, status: value })}
              >
                <Option value="pending">待处理</Option>
                <Option value="processing">处理中</Option>
                <Option value="resolved">已解决</Option>
                <Option value="closed">已关闭</Option>
              </Select>
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                onChange={(dates) => {
                  if (dates) {
                    handleSearch({
                      ...searchParams,
                      startDate: dates[0]?.format('YYYY-MM-DD'),
                      endDate: dates[1]?.format('YYYY-MM-DD'),
                    });
                  } else {
                    handleSearch({
                      ...searchParams,
                      startDate: undefined,
                      endDate: undefined,
                    });
                  }
                }}
              />
              <Button
                icon={<ReloadOutlined />}
                onClick={() => {
                  setSearchParams({});
                  fetchReports(1, pagination.pageSize, {});
                }}
              >
                重置
              </Button>
            </Space>
          </div>

          {/* 表格 */}
          <Table
            columns={columns}
            dataSource={reports}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ x: 1200 }}
          />

          {/* 分页 */}
          <Pagination
            current={pagination.current}
            total={pagination.total}
            pageSize={pagination.pageSize}
            onChange={handlePaginationChange}
          />
        </Card>

        {/* 分配模态框 */}
        <Modal
          title="分配故障报告"
          open={assignModalVisible}
          onCancel={() => setAssignModalVisible(false)}
          footer={null}
        >
          <Form
            form={assignForm}
            layout="vertical"
            onFinish={handleAssignSubmit}
          >
            <Form.Item
              label="负责人"
              name="assignedTo"
              rules={[{ required: true, message: '请输入负责人' }]}
            >
              <Input placeholder="请输入负责人姓名" />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  确认分配
                </Button>
                <Button onClick={() => setAssignModalVisible(false)}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>

        {/* 解决模态框 */}
        <Modal
          title="解决故障报告"
          open={resolveModalVisible}
          onCancel={() => setResolveModalVisible(false)}
          footer={null}
          width={600}
        >
          <Form
            form={resolveForm}
            layout="vertical"
            onFinish={handleResolveSubmit}
          >
            <Form.Item
              label="解决方案"
              name="solution"
              rules={[
                { required: true, message: '请输入解决方案' },
                { max: 500, message: '解决方案不能超过500个字符' },
              ]}
            >
              <TextArea
                rows={4}
                placeholder="请详细描述解决方案..."
                showCount
                maxLength={500}
              />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  确认解决
                </Button>
                <Button onClick={() => setResolveModalVisible(false)}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </PermissionGuard>
  );
};

export default FaultReports;
