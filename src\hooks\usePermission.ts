import { useMemo } from 'react';
import { useAuthStore } from '../store/authStore';
import { USER_ROLES } from '../constants';

/**
 * 权限管理Hook
 * 用于检查用户是否具有特定权限
 */
export const usePermission = () => {
  const { user } = useAuthStore();

  // 检查是否有特定权限
  const hasPermission = useMemo(() => {
    return (permission: string | string[]): boolean => {
      if (!user || !user.role) {
        return false;
      }

      // 超级管理员拥有所有权限
      if (user.role.name === USER_ROLES.SUPER_ADMIN) {
        return true;
      }

      const userPermissions = user.role.permissions?.map(p => p.code) || [];

      if (Array.isArray(permission)) {
        // 检查是否拥有数组中的任意一个权限
        return permission.some(p => userPermissions.includes(p));
      } else {
        // 检查是否拥有单个权限
        return userPermissions.includes(permission);
      }
    };
  }, [user]);

  // 检查是否有特定角色
  const hasRole = useMemo(() => {
    return (role: string | string[]): boolean => {
      if (!user || !user.role) {
        return false;
      }

      if (Array.isArray(role)) {
        return role.includes(user.role.name);
      } else {
        return user.role.name === role;
      }
    };
  }, [user]);

  // 检查是否是超级管理员
  const isSuperAdmin = useMemo(() => {
    return user?.role?.name === USER_ROLES.SUPER_ADMIN;
  }, [user]);

  // 检查是否是管理员（包括超级管理员和普通管理员）
  const isAdmin = useMemo(() => {
    return hasRole([USER_ROLES.SUPER_ADMIN, USER_ROLES.ADMIN]);
  }, [hasRole]);

  // 获取用户所有权限代码
  const permissions = useMemo(() => {
    return user?.role?.permissions?.map(p => p.code) || [];
  }, [user]);

  return {
    hasPermission,
    hasRole,
    isSuperAdmin,
    isAdmin,
    permissions,
    user,
  };
};
