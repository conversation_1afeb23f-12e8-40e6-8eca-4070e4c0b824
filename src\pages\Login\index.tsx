import React, { useState } from 'react';
import { Form, Input, But<PERSON>, Card, Typography, message, Spin } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router';
import { useAuthStore } from '../../store/authStore';
import { ROUTES } from '../../constants';
import { mockLogin } from '../../services/authService';
import './index.css';

const { Title, Text } = Typography;

interface LoginForm {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuthStore();

  // 获取重定向路径 - 使用 React Router v7 的新语法
  const from = location.state?.from?.pathname || ROUTES.DASHBOARD;

  const handleSubmit = async (values: LoginForm): Promise<void> => {
    setLoading(true);
    try {
      // 调用登录API
      const response = await mockLogin(values.username, values.password);

      if (response.success) {
        // 登录成功
        login(response.data.token, response.data.user);
        message.success('登录成功！');

        // 使用 React Router v7 的新导航方式
        navigate(from, {
          replace: true,
          state: { from: location.pathname }
        });
      } else {
        message.error(response.message || '登录失败');
      }
    } catch (error) {
      console.error('Login error:', error);
      message.error('登录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-content">
          <Card className="login-card">
            <div className="login-header">
              <div className="login-logo">📚</div>
              <Title level={2} className="login-title">
                图书馆管理系统
              </Title>
              <Text type="secondary">
                Library Management System
              </Text>
            </div>

            <Form
              form={form}
              name="login"
              onFinish={handleSubmit}
              autoComplete="off"
              size="large"
            >
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: '请输入用户名!' },
                  { min: 3, message: '用户名至少3个字符!' },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="用户名"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码!' },
                  { min: 6, message: '密码至少6个字符!' },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="密码"
                  autoComplete="current-password"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  className="login-button"
                  loading={loading}
                  block
                >
                  {loading ? <Spin size="small" /> : '登录'}
                </Button>
              </Form.Item>
            </Form>

            <div className="login-demo">
              <Text type="secondary" style={{ fontSize: '12px' }}>
                演示账号：
              </Text>
              <br />
              <Text code>admin / 123456</Text> (超级管理员)
              <br />
              <Text code>librarian / 123456</Text> (图书管理员)
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Login;
