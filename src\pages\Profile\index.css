.profile-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.profile-header {
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-header h2 {
  margin-bottom: 8px;
  color: #1890ff;
}

.avatar-card {
  text-align: center;
  height: fit-content;
}

.avatar-section {
  padding: 24px 0;
}

.profile-avatar {
  margin-bottom: 16px;
  border: 3px solid #f0f0f0;
}

.upload-btn {
  border-radius: 20px;
}

.user-info {
  text-align: left;
  padding: 0 8px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.info-item-horizontal {
  display: flex;
  align-items: center;
  padding: 8px;
  background: #fafafa;
  border-radius: 6px;
  min-height: 60px;
}

.info-content {
  flex: 1;
  min-width: 0;
}

.info-label {
  display: block;
  font-size: 12px;
  margin-bottom: 2px;
}

.info-value {
  display: block;
  font-size: 13px;
  word-break: break-all;
  line-height: 1.3;
}

.info-icon {
  margin-right: 8px;
  color: #1890ff;
  font-size: 14px;
  flex-shrink: 0;
}

.profile-form-card .ant-card-head {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.profile-form-card .ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

.profile-form-card .ant-card-body {
  padding: 24px;
}

/* 头像上传样式 */
.avatar-uploader .ant-upload {
  border: none;
  background: none;
}

.avatar-uploader .ant-upload:hover {
  border: none;
}

/* 表单样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input-affix-wrapper {
  border-radius: 6px;
}

.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 模态框样式 */
.ant-modal-header {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-title {
  font-weight: 600;
  color: #262626;
}

/* 文本域样式 */
.ant-input {
  border-radius: 6px;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 密码输入框样式 */
.ant-input-password {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 992px) {
  /* 在大平板和小屏幕上，左右分栏变为上下堆叠 */
  .avatar-card,
  .profile-form-card {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .profile-container {
    padding: 16px;
  }

  .profile-header {
    padding: 16px;
  }

  .avatar-section {
    padding: 16px 0;
  }

  .profile-avatar {
    width: 80px !important;
    height: 80px !important;
  }

  .user-info {
    padding: 0 4px;
  }

  .info-item {
    padding: 10px;
    margin-bottom: 12px;
  }

  .info-item-horizontal {
    padding: 6px;
    min-height: 50px;
  }

  .info-label {
    font-size: 11px;
  }

  .info-value {
    font-size: 12px;
  }

  .info-icon {
    font-size: 12px;
    margin-right: 6px;
  }

  /* 横向信息在小屏幕上变为单列 */
  .user-info .ant-col-8 {
    width: 100% !important;
    max-width: 100% !important;
    margin-bottom: 8px;
  }

  /* 表单在小屏幕上单列显示 */
  .ant-col-12 {
    width: 100% !important;
    max-width: 100% !important;
  }
}

@media (max-width: 576px) {
  .profile-container {
    padding: 12px;
  }

  .profile-header {
    padding: 12px;
  }

  .profile-header h2 {
    font-size: 20px;
  }

  .avatar-section {
    padding: 12px 0;
  }

  .profile-avatar {
    width: 70px !important;
    height: 70px !important;
  }

  .upload-btn {
    font-size: 11px;
    padding: 4px 8px;
  }

  .user-info {
    padding: 0 2px;
  }

  .info-item {
    padding: 8px;
    margin-bottom: 10px;
  }

  .info-item-horizontal {
    padding: 4px;
    min-height: 45px;
  }

  .info-label {
    font-size: 10px;
  }

  .info-value {
    font-size: 11px;
  }

  .info-icon {
    font-size: 11px;
    margin-right: 4px;
  }
}

/* 卡片阴影效果 */
.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: none;
}

.ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}

/* 图标颜色 */
.profile-form-card .ant-card-head-title .anticon {
  margin-right: 8px;
  color: #1890ff;
}

/* 输入框前缀图标 */
.ant-input-prefix {
  color: #8c8c8c;
}

/* 分割线样式 */
.ant-divider {
  margin: 16px 0;
  border-color: #f0f0f0;
}

/* 文本计数器样式 */
.ant-input-data-count {
  color: #8c8c8c;
  font-size: 12px;
}
