import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  DatePicker,
  Space,
  Typography,
  Tag,
  Modal,
  message,
  Tooltip,
  Row,
  Col,
  Statistic,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  SearchOutlined,
  ReloadOutlined,
  UndoOutlined,
  RedoOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import type { BorrowRecord } from '../../types/index';
import {
  getBorrowRecords,
  getBorrowStatistics,
  returnBook,
  renewBook
} from '../../services/borrowService';
import { PAGINATION_CONFIG, BORROW_STATUS } from '../../constants';
import { usePermission } from '../../hooks/usePermission';
import PermissionGuard from '../../components/PermissionGuard';
import Pagination from '../../components/Pagination';
import { PERMISSIONS } from '../../constants';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface SearchParams {
  bookTitle?: string;
  userName?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
}

interface Statistics {
  totalBorrows: number;
  currentBorrows: number;
  overdueBorrows: number;
  totalReturns: number;
  totalFines: number;
}

const BorrowRecords: React.FC = () => {
  const { hasPermission } = usePermission();

  const [records, setRecords] = useState<BorrowRecord[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [statistics, setStatistics] = useState<Statistics>({
    totalBorrows: 0,
    currentBorrows: 0,
    overdueBorrows: 0,
    totalReturns: 0,
    totalFines: 0,
  });
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: PAGINATION_CONFIG.DEFAULT_PAGE_SIZE,
    total: 0,
  });

  // 获取借还书记录
  const fetchRecords = async (page = 1, pageSize = pagination.pageSize, search = searchParams) => {
    setLoading(true);
    try {
      const response = await getBorrowRecords({
        page,
        pageSize,
        ...search,
      });

      if (response.success) {
        setRecords(response.data.list);
        setPagination({
          current: response.data.pagination.current,
          pageSize: response.data.pagination.pageSize,
          total: response.data.pagination.total,
        });
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('获取借还记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      const response = await getBorrowStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  useEffect(() => {
    fetchRecords();
    fetchStatistics();
  }, []);

  // 处理搜索
  const handleSearch = (values: SearchParams) => {
    setSearchParams(values);
    fetchRecords(1, pagination.pageSize, values);
  };

  // 处理分页变化
  const handlePaginationChange = (page: number, pageSize: number) => {
    fetchRecords(page, pageSize);
  };

  // 处理还书
  const handleReturn = (record: BorrowRecord) => {
    Modal.confirm({
      title: '确认还书',
      content: `确定要处理《${record.bookTitle}》的还书吗？`,
      onOk: async () => {
        try {
          const response = await returnBook(record.id);
          if (response.success) {
            message.success('还书成功');
            fetchRecords(pagination.current, pagination.pageSize);
            fetchStatistics();
          } else {
            message.error(response.message);
          }
        } catch (error) {
          message.error('还书失败');
        }
      },
    });
  };

  // 处理续借
  const handleRenew = (record: BorrowRecord) => {
    Modal.confirm({
      title: '确认续借',
      content: `确定要为《${record.bookTitle}》续借30天吗？`,
      onOk: async () => {
        try {
          const response = await renewBook(record.id);
          if (response.success) {
            message.success('续借成功');
            fetchRecords(pagination.current, pagination.pageSize);
          } else {
            message.error(response.message);
          }
        } catch (error) {
          message.error('续借失败');
        }
      },
    });
  };

  // 获取状态标签
  const getStatusTag = (status: string, dueDate: string) => {
    const isOverdue = dayjs().isAfter(dayjs(dueDate)) && status !== 'returned';

    if (status === 'returned') {
      return <Tag color="green">已归还</Tag>;
    } else if (isOverdue || status === 'overdue') {
      return <Tag color="red">逾期</Tag>;
    } else {
      return <Tag color="blue">借阅中</Tag>;
    }
  };

  // 表格列配置
  const columns: ColumnsType<BorrowRecord> = [
    {
      title: '图书名称',
      dataIndex: 'bookTitle',
      key: 'bookTitle',
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (title) => (
        <Tooltip placement="topLeft" title={title}>
          {title}
        </Tooltip>
      ),
    },
    {
      title: '借阅人',
      dataIndex: 'userName',
      key: 'userName',
      width: 100,
    },
    {
      title: '借阅日期',
      dataIndex: 'borrowDate',
      key: 'borrowDate',
      width: 120,
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '应还日期',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 120,
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '归还日期',
      dataIndex: 'returnDate',
      key: 'returnDate',
      width: 120,
      render: (date) => date ? dayjs(date).format('YYYY-MM-DD') : '-',
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (_, record) => getStatusTag(record.status, record.dueDate),
    },
    {
      title: '续借次数',
      dataIndex: 'renewCount',
      key: 'renewCount',
      width: 80,
      align: 'center',
    },
    {
      title: '罚金',
      dataIndex: 'fine',
      key: 'fine',
      width: 80,
      align: 'center',
      render: (fine) => fine ? `¥${fine}` : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          {record.status === 'borrowed' && (
            <PermissionGuard permission={PERMISSIONS.BORROW_MANAGE} showFallback={false}>
              <Tooltip title="还书">
                <Button
                  type="text"
                  icon={<UndoOutlined />}
                  onClick={() => handleReturn(record)}
                />
              </Tooltip>
              {record.renewCount < 2 && (
                <Tooltip title="续借">
                  <Button
                    type="text"
                    icon={<RedoOutlined />}
                    onClick={() => handleRenew(record)}
                  />
                </Tooltip>
              )}
            </PermissionGuard>
          )}
        </Space>
      ),
    },
  ];

  return (
    <PermissionGuard permission={PERMISSIONS.BORROW_VIEW}>
      <div>
        <Title level={2} style={{ marginBottom: 16 }}>借还记录</Title>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="总借阅量"
                value={statistics.totalBorrows}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="当前借阅"
                value={statistics.currentBorrows}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="逾期未还"
                value={statistics.overdueBorrows}
                valueStyle={{ color: '#f5222d' }}
                prefix={<ExclamationCircleOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="已归还"
                value={statistics.totalReturns}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={4}>
            <Card>
              <Statistic
                title="总罚金"
                value={statistics.totalFines}
                precision={2}
                prefix="¥"
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>

        <Card>
          {/* 搜索区域 */}
          <div style={{ marginBottom: 16 }}>
            <Space wrap>
              <Search
                placeholder="搜索图书名称"
                allowClear
                style={{ width: 200 }}
                onSearch={(value) => handleSearch({ ...searchParams, bookTitle: value })}
              />
              <Search
                placeholder="搜索借阅人"
                allowClear
                style={{ width: 150 }}
                onSearch={(value) => handleSearch({ ...searchParams, userName: value })}
              />
              <Select
                placeholder="选择状态"
                allowClear
                style={{ width: 120 }}
                onChange={(value) => handleSearch({ ...searchParams, status: value })}
              >
                <Option value={BORROW_STATUS.BORROWED}>借阅中</Option>
                <Option value={BORROW_STATUS.RETURNED}>已归还</Option>
                <Option value={BORROW_STATUS.OVERDUE}>逾期</Option>
              </Select>
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                onChange={(dates) => {
                  if (dates) {
                    handleSearch({
                      ...searchParams,
                      startDate: dates[0]?.format('YYYY-MM-DD'),
                      endDate: dates[1]?.format('YYYY-MM-DD'),
                    });
                  } else {
                    handleSearch({
                      ...searchParams,
                      startDate: undefined,
                      endDate: undefined,
                    });
                  }
                }}
              />
              <Button
                icon={<ReloadOutlined />}
                onClick={() => {
                  setSearchParams({});
                  fetchRecords(1, pagination.pageSize, {});
                }}
              >
                重置
              </Button>
            </Space>
          </div>

          {/* 表格 */}
          <Table
            columns={columns}
            dataSource={records}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ x: 1200 }}
          />

          {/* 分页 */}
          <Pagination
            current={pagination.current}
            total={pagination.total}
            pageSize={pagination.pageSize}
            onChange={handlePaginationChange}
          />
        </Card>
      </div>
    </PermissionGuard>
  );
};

export default BorrowRecords;
